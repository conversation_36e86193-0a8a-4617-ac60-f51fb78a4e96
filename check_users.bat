@echo off
echo Checking HealthPlus Database Users
echo ==================================
echo.

echo Attempting to connect with application credentials (heshan/pass)...
mysql -u heshan -ppass -e "USE test_HMS2; SELECT user_name, password, user_type FROM sys_user ORDER BY user_name;" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Failed with heshan/pass, trying with root...
    echo Please enter your MySQL root password:
    mysql -u root -p -e "USE test_HMS2; SELECT user_name, password, user_type FROM sys_user ORDER BY user_name;"
    if %ERRORLEVEL% NEQ 0 (
        echo ERROR: Cannot connect to database at all.
        echo Please make sure:
        echo 1. MySQL is running
        echo 2. Database test_HMS2 exists
        echo 3. Tables have been imported
        pause
        exit /b 1
    )
)

echo.
echo ==================================
echo Try logging in with any of the above username/password combinations
echo Most common password is: 1234
echo ==================================
pause
