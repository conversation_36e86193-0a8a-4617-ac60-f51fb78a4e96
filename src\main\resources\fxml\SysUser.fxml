<?xml version="1.0" encoding="UTF-8"?>

<?import java.net.*?>
<?import javafx.scene.chart.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.effect.*?>
<?import javafx.scene.paint.*?>
<?import javafx.scene.text.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.web.*?>
<?import java.lang.*?>
<?import java.util.*?>
<?import javafx.scene.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import de.jensd.fx.glyphs.*?>
<?import de.jensd.fx.glyphs.materialicons.*?>
<?import de.jensd.fx.glyphs.fontawesome.*?>
<?import de.jensd.fx.glyphs.materialdesignicons.*?>
<?import de.jensd.fx.glyphs.octicons.*?>
<?import de.jensd.fx.glyphs.weathericons.*?>
<?import jfxtras.scene.control.*?>
<?import jfxtras.scene.control.agenda.*?>
<?import org.controlsfx.glyphfont.*?>
<?import impl.org.controlsfx.autocompletion.*?>
<?import javafx.collections.FXCollections?>
<?import javafx.scene.control.cell.PropertyValueFactory?>
<?import javafx.scene.control.cell.*?>
<?import javafx.collections.*?>
<?import Doctor.*?>

<fx:root id="anchor" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="433.0" prefWidth="570.0" stylesheets="@../styles/popup.css" type="javafx.scene.layout.AnchorPane" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">        
    <children>
      <GridPane layoutX="129.0" layoutY="158.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
        <columnConstraints>
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="100.0" prefWidth="50.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="450.0" minWidth="450.0" prefWidth="450.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="100.0" prefWidth="50.0" />
        </columnConstraints>
        <rowConstraints>
            <RowConstraints maxHeight="1.7976931348623157E308" minHeight="100.0" prefHeight="100.0" vgrow="SOMETIMES" />
          <RowConstraints maxHeight="530.0" minHeight="530.0" prefHeight="530.0" vgrow="SOMETIMES" />
            <RowConstraints maxHeight="1.7976931348623157E308" minHeight="100.0" prefHeight="100.0" vgrow="SOMETIMES" />
        </rowConstraints>
         <children>
            <Button id="transparentButton2" fx:id="saveSuccess" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" mnemonicParsing="false" onAction="#saveSuccessExit" stylesheets="@../styles/box.css" GridPane.columnSpan="3" GridPane.rowSpan="3" />
            <Label id="box" fx:id="userType" alignment="TOP_LEFT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/popup.css" text="UserType" GridPane.columnIndex="1" GridPane.rowIndex="1" />
            <GridPane GridPane.columnIndex="1" GridPane.rowIndex="1">
              <columnConstraints>
                <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="70.0" prefWidth="70.0" />
              </columnConstraints>
              <rowConstraints>
                <RowConstraints maxHeight="35.0" minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
                <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="1.7976931348623157E308" minHeight="10.0" prefHeight="10.0" vgrow="SOMETIMES" />
              </rowConstraints>
               <children>
                  <Button id="dark-blue" fx:id="suspendButton" maxHeight="30.0" maxWidth="90.0" mnemonicParsing="false" onAction="#suspend" stylesheets="@../styles/buttons.css" text="suspend" GridPane.rowIndex="13">
                     <GridPane.margin>
                        <Insets left="340.0" />
                     </GridPane.margin>
                     
                  </Button>
                  <Label fx:id="close" alignment="TOP_RIGHT" contentDisplay="RIGHT" maxHeight="30.0" maxWidth="1.7976931348623157E308" onMouseClicked="#closeEditor">
                     <GridPane.margin>
                        <Insets left="320.0" right="5.0" top="5.0" />
                     </GridPane.margin>
                     <graphic>
                                             
                         <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="TIMES_CIRCLE" textFill="#333" />

                    </graphic>
                     <cursor>
                        <Cursor fx:constant="HAND" />
                     </cursor>
                  </Label>
                  <ComboBox id="inputText2" fx:id="gender" maxHeight="30.0" maxWidth="1.7976931348623157E308" prefWidth="150.0" stylesheets="@../styles/box.css" GridPane.rowIndex="8">
                     <items>
                        <FXCollections fx:factory="observableArrayList">
                           <String fx:value="Male" />
                           <String fx:value="Female" />
                        </FXCollections>
                     </items>
                     <GridPane.margin>
                        <Insets left="150.0" right="20.0" />
                     </GridPane.margin>
                  </ComboBox>
                  <DatePicker id="inputText2" fx:id="DOB" maxHeight="30.0" maxWidth="1.7976931348623157E308" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="5">
                     <GridPane.margin>
                        <Insets left="150.0" right="20.0" />
                     </GridPane.margin>
                  </DatePicker>
                  <TextField id="inputText2" fx:id="email" maxHeight="30.0" maxWidth="1.7976931348623157E308" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="7">
                     <GridPane.margin>
                        <Insets left="150.0" right="20.0" />
                     </GridPane.margin>
                  </TextField>
                  <TextField id="inputText2" fx:id="mobile" maxHeight="30.0" maxWidth="1.7976931348623157E308" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="6">
                     <GridPane.margin>
                        <Insets left="150.0" right="20.0" />
                     </GridPane.margin>
                  </TextField>
                  <TextField id="inputText2" fx:id="NIC" maxHeight="30.0" maxWidth="1.7976931348623157E308" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="3">
                     <GridPane.margin>
                        <Insets left="150.0" right="20.0" />
                     </GridPane.margin>
                  </TextField>
                  <TextField id="inputText2" fx:id="address" maxHeight="30.0" maxWidth="1.7976931348623157E308" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="4">
                     <GridPane.margin>
                        <Insets left="150.0" right="20.0" />
                     </GridPane.margin>
                  </TextField>
                  <TextField id="inputText2" fx:id="lastName" maxHeight="30.0" maxWidth="1.7976931348623157E308" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="2">
                     <GridPane.margin>
                        <Insets left="150.0" right="20.0" />
                     </GridPane.margin>
                  </TextField>
                  <TextField id="inputText2" fx:id="firstName" maxHeight="30.0" maxWidth="1.7976931348623157E308" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="1">
                     <GridPane.margin>
                        <Insets left="150.0" right="20.0" />
                     </GridPane.margin>
                  </TextField>
                  <Label id="inputLabel11" maxHeight="30.0" maxWidth="100.0" minHeight="30.0" minWidth="100.0" stylesheets="@../styles/box.css" text="Gender" GridPane.rowIndex="8">
                     <GridPane.margin>
                        <Insets left="50.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel11" maxHeight="30.0" maxWidth="100.0" minHeight="30.0" minWidth="100.0" stylesheets="@../styles/box.css" text="Email" GridPane.rowIndex="7">
                     <GridPane.margin>
                        <Insets left="50.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel11" maxHeight="30.0" maxWidth="100.0" minHeight="30.0" minWidth="100.0" stylesheets="@../styles/box.css" text="Mobile" GridPane.rowIndex="6">
                     <GridPane.margin>
                        <Insets left="50.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel11" maxHeight="30.0" maxWidth="100.0" minHeight="30.0" minWidth="100.0" stylesheets="@../styles/box.css" text="Date Of Birth" GridPane.rowIndex="5">
                     <GridPane.margin>
                        <Insets left="50.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel11" maxHeight="30.0" maxWidth="100.0" minHeight="30.0" minWidth="100.0" stylesheets="@../styles/box.css" text="Address" GridPane.rowIndex="4">
                     <GridPane.margin>
                        <Insets left="50.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel11" maxHeight="30.0" maxWidth="100.0" minHeight="30.0" minWidth="100.0" stylesheets="@../styles/box.css" text="NIC" GridPane.rowIndex="3">
                     <GridPane.margin>
                        <Insets left="50.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel11" maxHeight="30.0" maxWidth="100.0" minHeight="30.0" minWidth="100.0" stylesheets="@../styles/box.css" text="Last Name" GridPane.rowIndex="2">
                     <GridPane.margin>
                        <Insets left="50.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel11" maxHeight="30.0" maxWidth="100.0" minHeight="30.0" minWidth="100.0" stylesheets="@../styles/box.css" text="First Name" GridPane.rowIndex="1">
                     <GridPane.margin>
                        <Insets left="50.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="7">
                     <graphic>
                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="ENVELOPE" textFill="#333" />
                     </graphic>
                     <GridPane.margin>
                        <Insets left="20.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="6">
                     <graphic>
                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="MOBILE" textFill="#333" />
                     </graphic>
                     <GridPane.margin>
                        <Insets left="20.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="5">
                     <graphic>
                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="CALENDAR" textFill="#333" />
                     </graphic>
                     <GridPane.margin>
                        <Insets left="20.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="3">
                     <graphic>
                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="LIST_ALT" textFill="#333" />
                     </graphic>
                     <GridPane.margin>
                        <Insets left="20.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="4">
                     <graphic>
                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="MAP_MARKER" textFill="#333" />
                     </graphic>
                     <GridPane.margin>
                        <Insets left="20.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="8">
                     <graphic>
                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="MALE" textFill="#333" />
                     </graphic>
                     <GridPane.margin>
                        <Insets left="20.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="2">
                     <graphic>
                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="INFO_CIRCLE" textFill="#333" />
                     </graphic>
                     <GridPane.margin>
                        <Insets left="20.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="1">
                     <graphic>
                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="INFO_CIRCLE" textFill="#333" />
                     </graphic>
                     <GridPane.margin>
                        <Insets left="20.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel11" maxHeight="30.0" maxWidth="100.0" minHeight="30.0" minWidth="100.0" stylesheets="@../styles/box.css" text="User Name" GridPane.rowIndex="10">
                     <GridPane.margin>
                        <Insets left="50.0" />
                     </GridPane.margin>
                  </Label>
                  <TextField id="inputText2" fx:id="username" maxHeight="30.0" maxWidth="1.7976931348623157E308" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="10">
                     <GridPane.margin>
                        <Insets left="150.0" right="20.0" />
                     </GridPane.margin>
                  </TextField>
                  <TextField id="inputText2" fx:id="userid" maxHeight="30.0" maxWidth="1.7976931348623157E308" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="11">
                     <GridPane.margin>
                        <Insets left="150.0" right="20.0" />
                     </GridPane.margin>
                  </TextField>
                  <Label id="inputLabel11" maxHeight="30.0" maxWidth="100.0" minHeight="30.0" minWidth="100.0" stylesheets="@../styles/box.css" text="User ID" GridPane.rowIndex="11">
                     <GridPane.margin>
                        <Insets left="50.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="10">
                     <graphic>
                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="INFO_CIRCLE" textFill="#333" />
                     </graphic>
                     <GridPane.margin>
                        <Insets left="20.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="11">
                     <graphic>
                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="LIST_ALT" textFill="#333" />
                     </graphic>
                     <GridPane.margin>
                        <Insets left="20.0" />
                     </GridPane.margin>
                  </Label>
                  <Button id="dark-blue" fx:id="resetPasswordButton" maxHeight="30.0" maxWidth="120.0" mnemonicParsing="false" onAction="#resetPassword" stylesheets="@../styles/buttons.css" text="Reset Password" GridPane.rowIndex="13">
                     <GridPane.margin>
                        <Insets left="200.0" />
                     </GridPane.margin>
                  </Button>        
               </children>
            </GridPane>
         </children>
      </GridPane>
   </children>
</fx:root>
