@echo off
echo Trying Windows Authentication for MySQL
echo ========================================
echo.

echo Attempting to connect using Windows Authentication...
mysql --protocol=pipe -e "SELECT 'Connected successfully!' as Status;" 2>nul
if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: Connected with Windows Authentication!
    echo Setting up database...
    mysql --protocol=pipe -e "CREATE DATABASE IF NOT EXISTS test_HMS2;"
    mysql --protocol=pipe -e "CREATE USER IF NOT EXISTS 'heshan'@'localhost' IDENTIFIED BY 'pass'; GRANT ALL PRIVILEGES ON test_HMS2.* TO 'heshan'@'localhost'; FLUSH PRIVILEGES;"
    mysql --protocol=pipe test_HMS2 < database\hms_db.sql
    echo Database setup complete!
    mysql --protocol=pipe -e "USE test_HMS2; SELECT user_name, password, user_type FROM sys_user LIMIT 5;"
) else (
    echo Windows Authentication failed.
    echo Please try the MySQL password reset method.
)

pause
