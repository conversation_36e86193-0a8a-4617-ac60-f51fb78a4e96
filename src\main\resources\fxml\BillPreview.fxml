<?xml version="1.0" encoding="UTF-8"?>

<?import java.net.*?>
<?import javafx.scene.chart.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.effect.*?>
<?import javafx.scene.paint.*?>
<?import javafx.scene.text.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.web.*?>
<?import java.lang.*?>
<?import java.util.*?>
<?import javafx.scene.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import de.jensd.fx.glyphs.*?>
<?import de.jensd.fx.glyphs.materialicons.*?>
<?import de.jensd.fx.glyphs.fontawesome.*?>
<?import de.jensd.fx.glyphs.materialdesignicons.*?>
<?import de.jensd.fx.glyphs.octicons.*?>
<?import de.jensd.fx.glyphs.weathericons.*?>
<?import jfxtras.scene.control.*?>
<?import jfxtras.scene.control.agenda.*?>
<?import org.controlsfx.glyphfont.*?>
<?import impl.org.controlsfx.autocompletion.*?>
<?import javafx.collections.FXCollections?>
<?import javafx.scene.control.cell.PropertyValueFactory?>
<?import javafx.scene.control.cell.*?>
<?import javafx.collections.*?>
<?import Doctor.*?>

<fx:root id="anchor" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/popup.css" type="javafx.scene.layout.AnchorPane" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">        
    <children>
      <GridPane layoutX="129.0" layoutY="158.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
        <columnConstraints>
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="100.0" prefWidth="50.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="400.0" minWidth="400.0" prefWidth="400.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="100.0" prefWidth="50.0" />
        </columnConstraints>
        <rowConstraints>
            <RowConstraints maxHeight="1.7976931348623157E308" minHeight="100.0" prefHeight="100.0" vgrow="SOMETIMES" />
          <RowConstraints maxHeight="360.0" minHeight="360.0" prefHeight="360.0" vgrow="SOMETIMES" />
            <RowConstraints maxHeight="1.7976931348623157E308" minHeight="100.0" prefHeight="100.0" vgrow="SOMETIMES" />
        </rowConstraints>
         <children>
            <Button id="transparentButton2" fx:id="saveSuccess" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" mnemonicParsing="false" onAction="#saveSuccessExit" stylesheets="@../styles/box.css" GridPane.columnSpan="5" GridPane.rowSpan="3" />
            <Label id="box" alignment="TOP_LEFT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/popup.css" text="Paid" GridPane.columnIndex="1" GridPane.rowIndex="1">
               <font>
                  <Font name="System Bold" size="13.0" />
               </font>
            </Label>
            <GridPane GridPane.columnIndex="1" GridPane.rowIndex="1">
              <columnConstraints>
                <ColumnConstraints hgrow="SOMETIMES" maxWidth="140.0" minWidth="140.0" prefWidth="140.0" />
                <ColumnConstraints hgrow="SOMETIMES" maxWidth="260.0" minWidth="260.0" prefWidth="260.0" />
              </columnConstraints>
              <rowConstraints>
                <RowConstraints maxHeight="60.0" minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
                <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                  <RowConstraints minHeight="5.0" prefHeight="30.0" vgrow="SOMETIMES" />
              </rowConstraints>
               <children>
                  <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Patient" GridPane.rowIndex="1">
                     <GridPane.margin>
                        <Insets left="50.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Bill" GridPane.rowIndex="4">
                     <GridPane.margin>
                        <Insets left="50.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Date" GridPane.rowIndex="5">
                     <GridPane.margin>
                        <Insets left="50.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Bill ID" GridPane.rowIndex="6">
                     <GridPane.margin>
                        <Insets left="50.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputText3" fx:id="name" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="1">
                     <GridPane.margin>
                        <Insets right="40.0" />
                     </GridPane.margin></Label>
                  <Label id="inputText3" fx:id="bill" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="4">
                     <GridPane.margin>
                        <Insets right="40.0" />
                     </GridPane.margin></Label>
                  <Label id="inputText3" fx:id="date" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="5">
                     <GridPane.margin>
                        <Insets right="40.0" />
                     </GridPane.margin></Label>
                  <Label id="inputText3" fx:id="billID" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="6">
                     <GridPane.margin>
                        <Insets right="40.0" />
                     </GridPane.margin></Label>
                  <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" VAT" GridPane.rowIndex="3">
                     <GridPane.margin>
                        <Insets left="50.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Service Fee" GridPane.rowIndex="2">
                     <GridPane.margin>
                        <Insets left="50.0" />
                     </GridPane.margin>
                  </Label>
                  <Label id="inputText3" fx:id="service" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="2">
                     <GridPane.margin>
                        <Insets right="40.0" />
                     </GridPane.margin></Label>
                  <Label id="inputText3" fx:id="vat" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="3">
                     <GridPane.margin>
                        <Insets right="40.0" />
                     </GridPane.margin></Label>
                  <ProgressIndicator fx:id="saveProgress" progress="1" GridPane.columnIndex="1">
                     <GridPane.margin>
                        <Insets bottom="5.0" left="200.0" top="5.0" />
                     </GridPane.margin>
                  </ProgressIndicator>
                  <Button id="dark-blue" maxHeight="30.0" maxWidth="60.0" mnemonicParsing="false" stylesheets="@../styles/buttons.css" text="Print" GridPane.columnIndex="1" GridPane.rowIndex="7">
                     <GridPane.margin>
                        <Insets left="160.0" />
                     </GridPane.margin>
                  </Button>
               </children>
            </GridPane>
         </children>
      </GridPane>
   </children>
</fx:root>
