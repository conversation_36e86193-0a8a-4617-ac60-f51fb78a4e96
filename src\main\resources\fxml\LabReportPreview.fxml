<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.chart.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.effect.*?>
<?import javafx.scene.paint.*?>
<?import javafx.scene.text.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.web.*?>
<?import java.lang.*?>
<?import java.util.*?>
<?import javafx.scene.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import de.jensd.fx.glyphs.*?>
<?import de.jensd.fx.glyphs.materialicons.*?>
<?import de.jensd.fx.glyphs.fontawesome.*?>
<?import de.jensd.fx.glyphs.materialdesignicons.*?>
<?import de.jensd.fx.glyphs.octicons.*?>
<?import de.jensd.fx.glyphs.weathericons.*?>
<?import jfxtras.scene.control.*?>
<?import jfxtras.scene.control.agenda.*?>
<?import org.controlsfx.glyphfont.*?>
<?import impl.org.controlsfx.autocompletion.*?>
<?import javafx.collections.FXCollections?>
<?import javafx.scene.control.cell.PropertyValueFactory?>
<?import javafx.scene.control.cell.*?>
<?import javafx.collections.*?>
<?import LabAssistant.*?>

<fx:root id="anchor" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="600.0" prefWidth="900.0" stylesheets="@../styles/popup.css" type="javafx.scene.layout.AnchorPane" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">   <children>
      <Label layoutX="145.0" layoutY="251.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0" />
      <GridPane layoutX="129.0" layoutY="158.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
        <columnConstraints>
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="50.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="500.0" minWidth="500.0" prefWidth="500.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="50.0" prefWidth="50.0" />
        </columnConstraints>
        <rowConstraints>
            <RowConstraints maxHeight="1.7976931348623157E308" minHeight="20.0" prefHeight="50.0" vgrow="SOMETIMES" />
          <RowConstraints maxHeight="400.0" minHeight="-Infinity" prefHeight="400.0" vgrow="SOMETIMES" />
            <RowConstraints maxHeight="1.7976931348623157E308" minHeight="20.0" prefHeight="50.0" vgrow="SOMETIMES" />
        </rowConstraints>
         <children>
            <Label id="box" fx:id="tableHeader" alignment="TOP_LEFT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/popup.css" text="Report" GridPane.columnIndex="1" GridPane.rowIndex="1">
               <font>
                  <Font name="System Bold" size="16.0" />
               </font></Label>
            <GridPane GridPane.columnIndex="1" GridPane.rowIndex="1">
              <columnConstraints>
                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" />
                <ColumnConstraints hgrow="SOMETIMES" maxWidth="75.0" minWidth="75.0" prefWidth="75.0" />
              </columnConstraints>
              <rowConstraints>
                <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="1.7976931348623157E308" minHeight="-Infinity" vgrow="SOMETIMES" />
                <RowConstraints maxHeight="60.0" minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
              </rowConstraints>
               <children>
                  <Button id="transparentButton" fx:id="closeAccounts" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" mnemonicParsing="false" onAction="#closeViewAccounts" stylesheets="@../styles/box.css" GridPane.columnIndex="1">
                     <GridPane.margin>
                        <Insets bottom="10.0" left="40.0" />
                     </GridPane.margin>
                     <graphic>
                                             
                         <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="TIMES_CIRCLE" textFill="#303641" />

                    </graphic>
                     <cursor>
                        <Cursor fx:constant="HAND" />
                     </cursor>
                  </Button>
                  <GridPane GridPane.columnSpan="2" GridPane.rowIndex="2">
                    <columnConstraints>
                      <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="90.0" minWidth="10.0" prefWidth="90.0" />
                    </columnConstraints>
                    <rowConstraints>
                      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                    </rowConstraints>
                     <children>
                        <Button id="dark-blue" maxHeight="30.0" maxWidth="80.0" mnemonicParsing="false" stylesheets="@../styles/buttons.css" text="print" GridPane.columnIndex="1" />
                     </children>
                  </GridPane>
                  <TableView fx:id="report" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/table2.css" GridPane.columnSpan="2" GridPane.rowIndex="1">
                    <columnResizePolicy>
                        <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
                    </columnResizePolicy>
                    <columns>
                        
                      <TableColumn prefWidth="242.0" text="Constituent">
                          <cellValueFactory><PropertyValueFactory property="constituent" /></cellValueFactory>
                      </TableColumn>   
                      <TableColumn minWidth="0.0" prefWidth="233.0" text="Result">
                          <cellValueFactory><PropertyValueFactory property="result" /></cellValueFactory>
                      </TableColumn> 
                      
                    </columns>
                     <GridPane.margin>
                        <Insets left="10.0" right="10.0" />
                     </GridPane.margin>
                  </TableView>
               </children>
            </GridPane>
         </children>
      </GridPane>
   </children>
</fx:root>
