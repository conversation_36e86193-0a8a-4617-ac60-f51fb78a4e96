# HealthPlus Complete MySQL Setup Script
Write-Host "HealthPlus Complete MySQL Setup" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host ""

# Step 1: Stop MySQL Service
Write-Host "Step 1: Stopping MySQL service..." -ForegroundColor Yellow
try {
    Stop-Service -Name "mysql80" -Force -ErrorAction SilentlyContinue
    Write-Host "MySQL80 service stopped" -ForegroundColor Green
} catch {
    try {
        Stop-Service -Name "mysql" -Force -ErrorAction SilentlyContinue
        Write-Host "MySQL service stopped" -ForegroundColor Green
    } catch {
        Write-Host "Could not stop MySQL service - it might not be running" -ForegroundColor Yellow
    }
}

Start-Sleep -Seconds 3

# Step 2: Create password reset file
Write-Host "Step 2: Creating password reset file..." -ForegroundColor Yellow
$resetSQL = "ALTER USER 'root'@'localhost' IDENTIFIED BY 'newpassword123';"
$resetSQL | Out-File -FilePath "mysql_reset.sql" -Encoding ASCII
Write-Host "Reset file created" -ForegroundColor Green

# Step 3: Start MySQL with reset file
Write-Host "Step 3: Starting MySQL with password reset..." -ForegroundColor Yellow
$mysqldProcess = Start-Process -FilePath "mysqld" -ArgumentList "--init-file=`"$PWD\mysql_reset.sql`"" -PassThru -WindowStyle Hidden
Start-Sleep -Seconds 10
Write-Host "MySQL started with reset file" -ForegroundColor Green

# Step 4: Test new password
Write-Host "Step 4: Testing new password..." -ForegroundColor Yellow
$testResult = & mysql -u root -pnewpassword123 -e "SELECT 'Password reset successful!' as Status;" 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "Password reset successful!" -ForegroundColor Green
} else {
    Write-Host "Password reset failed. Trying alternative method..." -ForegroundColor Red
    
    # Kill mysqld process and try safe mode
    if ($mysqldProcess) { $mysqldProcess.Kill() }
    Start-Sleep -Seconds 3
    
    # Start in safe mode
    Write-Host "Starting MySQL in safe mode..." -ForegroundColor Yellow
    $safeModeProcess = Start-Process -FilePath "mysqld" -ArgumentList "--skip-grant-tables", "--skip-networking" -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 10
    
    # Reset password in safe mode
    $resetCommands = @"
USE mysql;
ALTER USER 'root'@'localhost' IDENTIFIED BY 'newpassword123';
FLUSH PRIVILEGES;
"@
    $resetCommands | & mysql -u root
    
    # Kill safe mode process
    if ($safeModeProcess) { $safeModeProcess.Kill() }
    Start-Sleep -Seconds 3
}

# Step 5: Start MySQL normally
Write-Host "Step 5: Starting MySQL service normally..." -ForegroundColor Yellow
try {
    Start-Service -Name "mysql80" -ErrorAction SilentlyContinue
    Write-Host "MySQL80 service started" -ForegroundColor Green
} catch {
    try {
        Start-Service -Name "mysql" -ErrorAction SilentlyContinue
        Write-Host "MySQL service started" -ForegroundColor Green
    } catch {
        Write-Host "Could not start MySQL service as Windows service, trying direct start..." -ForegroundColor Yellow
        Start-Process -FilePath "mysqld" -WindowStyle Hidden
        Start-Sleep -Seconds 10
    }
}

# Step 6: Set up HealthPlus database
Write-Host "Step 6: Setting up HealthPlus database..." -ForegroundColor Yellow

# Create database
& mysql -u root -pnewpassword123 -e "CREATE DATABASE IF NOT EXISTS test_HMS2;"
if ($LASTEXITCODE -eq 0) {
    Write-Host "Database test_HMS2 created" -ForegroundColor Green
} else {
    Write-Host "Failed to create database" -ForegroundColor Red
    exit 1
}

# Create user
& mysql -u root -pnewpassword123 -e "CREATE USER IF NOT EXISTS 'heshan'@'localhost' IDENTIFIED BY 'pass'; GRANT ALL PRIVILEGES ON test_HMS2.* TO 'heshan'@'localhost'; FLUSH PRIVILEGES;"
if ($LASTEXITCODE -eq 0) {
    Write-Host "User 'heshan' created and permissions granted" -ForegroundColor Green
} else {
    Write-Host "Failed to create user" -ForegroundColor Red
}

# Import database schema
Write-Host "Importing database schema and data..." -ForegroundColor Yellow
& mysql -u root -pnewpassword123 test_HMS2 -e "source database/hms_db.sql"
if ($LASTEXITCODE -eq 0) {
    Write-Host "Database schema imported successfully" -ForegroundColor Green
} else {
    Write-Host "Failed to import database schema" -ForegroundColor Red
}

# Step 7: Verify setup
Write-Host "Step 7: Verifying setup..." -ForegroundColor Yellow
Write-Host "Available users for login:" -ForegroundColor Cyan
& mysql -u root -pnewpassword123 -e "USE test_HMS2; SELECT CONCAT('Username: ', user_name, ' | Password: ', password, ' | Role: ', user_type) as 'Login Credentials' FROM sys_user WHERE user_name IN ('user001', 'user012', 'user016', 'user018', 'user020', 'user021') ORDER BY user_name;"

# Clean up
Remove-Item -Path "mysql_reset.sql" -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "================================" -ForegroundColor Green
Write-Host "Setup Complete!" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host "MySQL root password is now: newpassword123" -ForegroundColor Yellow
Write-Host "You can now run the HealthPlus application with: mvn javafx:run" -ForegroundColor Cyan
Write-Host "Try logging in with:" -ForegroundColor Cyan
Write-Host "- Admin: user021 / 1234" -ForegroundColor White
Write-Host "- Doctor: user001 / 1234" -ForegroundColor White
Write-Host "- Pharmacist: user016 / 1234" -ForegroundColor White
Write-Host ""
