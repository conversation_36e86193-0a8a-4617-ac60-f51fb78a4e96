@echo off
echo HealthPlus Login Debug Script
echo =============================
echo.

echo Checking database connection and user data...
echo.

REM Try with heshan/pass first (application credentials)
echo Testing application database connection (heshan/pass)...
mysql -u heshan -ppass -e "USE test_HMS2; SELECT user_name, password, user_type FROM sys_user ORDER BY user_name;" 2>nul
if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: Connected with heshan/pass
    echo.
    echo Available users for login:
    mysql -u heshan -ppass -e "USE test_HMS2; SELECT CONCAT('Username: ', user_name, ' | Password: ', password, ' | Role: ', user_type) as 'Login Info' FROM sys_user WHERE user_name IN ('user001', 'user012', 'user016', 'user018', 'user020', 'user021') ORDER BY user_name;"
    echo.
    echo Try logging in with any of the above combinations.
    echo Most common password should be: 1234
) else (
    echo FAILED: Cannot connect with heshan/pass
    echo Trying with root credentials...
    echo Please enter your MySQL root password:
    mysql -u root -p -e "USE test_HMS2; SELECT user_name, password, user_type FROM sys_user WHERE user_name IN ('user001', 'user012', 'user016', 'user018', 'user020', 'user021') ORDER BY user_name;"
    if %ERRORLEVEL% NEQ 0 (
        echo ERROR: Cannot connect to database at all.
        echo Please ensure:
        echo 1. MySQL is running
        echo 2. Database test_HMS2 exists  
        echo 3. Database has been imported
        pause
        exit /b 1
    )
)

echo.
echo =============================
echo If you see users above, try logging in with those exact credentials.
echo If passwords don't work, we can reset them.
echo =============================
pause
