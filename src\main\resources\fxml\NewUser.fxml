<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.chart.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.effect.*?>
<?import javafx.scene.paint.*?>
<?import javafx.scene.text.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.web.*?>
<?import java.lang.*?>
<?import java.util.*?>
<?import javafx.scene.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import de.jensd.fx.glyphs.*?>
<?import de.jensd.fx.glyphs.materialicons.*?>
<?import de.jensd.fx.glyphs.fontawesome.*?>
<?import de.jensd.fx.glyphs.materialdesignicons.*?>
<?import de.jensd.fx.glyphs.octicons.*?>
<?import de.jensd.fx.glyphs.weathericons.*?>
<?import jfxtras.scene.control.*?>
<?import jfxtras.scene.control.agenda.*?>
<?import org.controlsfx.glyphfont.*?>
<?import impl.org.controlsfx.autocompletion.*?>
<?import javafx.collections.FXCollections?>
<?import javafx.scene.control.cell.PropertyValueFactory?>
<?import javafx.scene.control.cell.*?>
<?import javafx.collections.*?>
<?import Pharmacist.*?>

<fx:root id="anchor" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="180.0" prefWidth="400.0" stylesheets="@../styles/popup.css" type="javafx.scene.layout.AnchorPane" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
   <children>
      <Label layoutX="145.0" layoutY="251.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0" />
      <GridPane layoutX="129.0" layoutY="158.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
        <columnConstraints>
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="100.0" prefWidth="600.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="400.0" minWidth="400.0" prefWidth="400.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="100.0" prefWidth="600.0" />
        </columnConstraints>
        <rowConstraints>
            <RowConstraints maxHeight="1.7976931348623157E308" minHeight="100.0" prefHeight="100.0" vgrow="SOMETIMES" />
          <RowConstraints maxHeight="260.0" minHeight="260.0" prefHeight="260.0" vgrow="SOMETIMES" />
            <RowConstraints maxHeight="1.7976931348623157E308" minHeight="100.0" prefHeight="100.0" vgrow="SOMETIMES" />
        </rowConstraints>
         <children>
            <Label id="box" alignment="TOP_LEFT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/popup.css" text="Account Created" GridPane.columnIndex="1" GridPane.rowIndex="1">
               <font>
                  <Font name="System Bold" size="16.0" />
               </font></Label>
            <GridPane GridPane.columnIndex="1" GridPane.rowIndex="1">
              <columnConstraints>
                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="343.0" minWidth="10.0" prefWidth="306.0" />
                <ColumnConstraints hgrow="SOMETIMES" maxWidth="70.0" minWidth="70.0" prefWidth="70.0" />
                <ColumnConstraints hgrow="SOMETIMES" maxWidth="70.0" minWidth="70.0" prefWidth="70.0" />
                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="20.0" minWidth="20.0" prefWidth="20.0" />
              </columnConstraints>
              <rowConstraints>
                <RowConstraints maxHeight="80.0" minHeight="80.0" prefHeight="80.0" vgrow="SOMETIMES" />
                <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                <RowConstraints maxHeight="60.0" minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
              </rowConstraints>
               <children>
                  <Button id="dark-blue" fx:id="closeUser" maxHeight="30.0" maxWidth="65.0" minHeight="30.0" minWidth="65.0" mnemonicParsing="false" onAction="#closeUserWindow" stylesheets="@../styles/buttons.css" text="OK" GridPane.columnIndex="2" GridPane.rowIndex="4" />
                  <GridPane GridPane.columnSpan="4" GridPane.rowIndex="1">
                    <columnConstraints>
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="20.0" minWidth="20.0" prefWidth="20.0" />
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="40.0" minWidth="40.0" prefWidth="40.0" />
                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="100.0" minWidth="10.0" prefWidth="100.0" />
                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="400.0" minWidth="10.0" prefWidth="207.0" />
                    </columnConstraints>
                    <rowConstraints>
                      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                    </rowConstraints>
                     <children>
                        <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" prefHeight="30.0" prefWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1">
                           <GridPane.margin>
                              <Insets left="10.0" />
                           </GridPane.margin>
                           
                           <graphic>   
                              <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="BULLSEYE" textFill="#333" />
                            </graphic>
                            
                        </Label>
                        <TextField id="inputText2" fx:id="userName" editable="false" maxHeight="30.0" minHeight="30.0" prefHeight="25.0" prefWidth="174.0" stylesheets="@../styles/box.css" GridPane.columnIndex="3">
                           <GridPane.margin>
                              <Insets right="30.0" />
                           </GridPane.margin>
                        </TextField>
                        <Label id="inputLabel11" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="User Name" GridPane.columnIndex="2" />
                     </children>
                  </GridPane>
                  <GridPane GridPane.columnSpan="4" GridPane.rowIndex="3">
                     <children>
                        <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" prefHeight="30.0" prefWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1">
                           <graphic>
                              <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="CALENDAR" textFill="#333" />
                           </graphic>
                           <GridPane.margin>
                              <Insets left="10.0" />
                           </GridPane.margin>
                        </Label>
                        <TextField id="inputText2" fx:id="password" editable="false" maxHeight="30.0" minHeight="30.0" prefHeight="25.0" prefWidth="174.0" stylesheets="@../styles/box.css" GridPane.columnIndex="3">
                           <GridPane.margin>
                              <Insets right="30.0" />
                           </GridPane.margin>
                        </TextField>
                        <Label id="inputLabel11" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Password" GridPane.columnIndex="2" />
                     </children>
                     <columnConstraints>
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="20.0" minWidth="20.0" prefWidth="20.0" />
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="40.0" minWidth="40.0" prefWidth="40.0" />
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="100.0" minWidth="10.0" prefWidth="100.0" />
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="400.0" minWidth="10.0" prefWidth="207.0" />
                     </columnConstraints>
                     <rowConstraints>
                        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                     </rowConstraints>
                  </GridPane>
                  <GridPane GridPane.columnSpan="4" GridPane.rowIndex="2">
                     <children>
                        <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" prefHeight="30.0" prefWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1">
                           <graphic>
                              <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="ENVELOPE" textFill="#333" />
                           </graphic>
                           <GridPane.margin>
                              <Insets left="10.0" />
                           </GridPane.margin>
                        </Label>
                        <TextField id="inputText2" fx:id="userid" editable="false" maxHeight="30.0" minHeight="30.0" prefHeight="25.0" prefWidth="174.0" stylesheets="@../styles/box.css" GridPane.columnIndex="3">
                           <GridPane.margin>
                              <Insets right="30.0" />
                           </GridPane.margin>
                        </TextField>
                        <Label id="inputLabel11" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="User ID" GridPane.columnIndex="2">
                           <GridPane.margin>
                              <Insets />
                           </GridPane.margin>
                        </Label>
                     </children>
                     <columnConstraints>
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="20.0" minWidth="20.0" prefWidth="20.0" />
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="40.0" minWidth="40.0" prefWidth="40.0" />
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="100.0" minWidth="10.0" prefWidth="100.0" />
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="400.0" minWidth="10.0" prefWidth="207.0" />
                     </columnConstraints>
                     <rowConstraints>
                        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                     </rowConstraints>
                  </GridPane>
                  <ProgressIndicator progress="1.0" GridPane.columnIndex="2">
                     <GridPane.margin>
                        <Insets top="10.0" />
                     </GridPane.margin>
                  </ProgressIndicator>
               </children>
            </GridPane>
         </children>
      </GridPane>
   </children>
</fx:root>
