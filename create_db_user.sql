-- Create database user for HealthPlus application
-- This matches the credentials in config.properties

-- Create the database user 'heshan' with password 'pass'
CREATE USER IF NOT EXISTS 'heshan'@'localhost' IDENTIFIED BY 'pass';

-- Grant all privileges on the test_HMS2 database to the user
GRANT ALL PRIVILEGES ON test_HMS2.* TO 'heshan'@'localhost';

-- Also grant privileges for creating/dropping databases (for admin functions)
GRANT CREATE, DROP ON *.* TO 'heshan'@'localhost';

-- Flush privileges to ensure changes take effect
FLUSH PRIVILEGES;

-- Show the user was created successfully
SELECT User, Host FROM mysql.user WHERE User = 'heshan';
