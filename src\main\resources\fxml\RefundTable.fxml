<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.chart.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.effect.*?>
<?import javafx.scene.paint.*?>
<?import javafx.scene.text.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.web.*?>
<?import java.lang.*?>
<?import java.util.*?>
<?import javafx.scene.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import de.jensd.fx.glyphs.*?>
<?import de.jensd.fx.glyphs.materialicons.*?>
<?import de.jensd.fx.glyphs.fontawesome.*?>
<?import de.jensd.fx.glyphs.materialdesignicons.*?>
<?import de.jensd.fx.glyphs.octicons.*?>
<?import de.jensd.fx.glyphs.weathericons.*?>
<?import jfxtras.scene.control.*?>
<?import jfxtras.scene.control.agenda.*?>
<?import org.controlsfx.glyphfont.*?>
<?import impl.org.controlsfx.autocompletion.*?>
<?import javafx.collections.FXCollections?>
<?import javafx.scene.control.cell.PropertyValueFactory?>
<?import javafx.scene.control.cell.*?>
<?import javafx.collections.*?>
<?import Cashier.*?>

<fx:root id="anchor" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/popup.css" type="javafx.scene.layout.AnchorPane" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1"> 
   <children>
      <Label layoutX="145.0" layoutY="251.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0" />
      <GridPane layoutX="129.0" layoutY="158.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
        <columnConstraints>
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="50.0" prefWidth="50.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="900.0" minWidth="600.0" prefWidth="600.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="50.0" prefWidth="50.0" />
        </columnConstraints>
        <rowConstraints>
            <RowConstraints maxHeight="1.7976931348623157E308" minHeight="100.0" prefHeight="100.0" vgrow="SOMETIMES" />
          <RowConstraints maxHeight="380.0" minHeight="380.0" prefHeight="380.0" vgrow="SOMETIMES" />
            <RowConstraints maxHeight="1.7976931348623157E308" minHeight="100.0" prefHeight="100.0" vgrow="SOMETIMES" />
        </rowConstraints>
         <children>
            <Label id="box" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/popup.css" GridPane.columnIndex="1" GridPane.rowIndex="1" />
            <GridPane GridPane.columnIndex="1" GridPane.rowIndex="1">
              <columnConstraints>
                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="20.0" minWidth="20.0" prefWidth="20.0" />
                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="306.0" />
                <ColumnConstraints hgrow="SOMETIMES" maxWidth="70.0" minWidth="70.0" prefWidth="70.0" />
                <ColumnConstraints hgrow="SOMETIMES" maxWidth="70.0" minWidth="70.0" prefWidth="70.0" />
                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="20.0" minWidth="20.0" prefWidth="20.0" />
              </columnConstraints>
              <rowConstraints>
                <RowConstraints maxHeight="60.0" minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
                <RowConstraints maxHeight="280.0" minHeight="280.0" prefHeight="280.0" vgrow="SOMETIMES" />
                <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
              </rowConstraints>
               <children>
                  <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Refund Details" GridPane.columnIndex="1">
                     <GridPane.margin>
                        <Insets />
                     </GridPane.margin>
                     <font>
                        <Font name="System Bold" size="18.0" />
                     </font>
                  </Label>
                  <GridPane GridPane.columnIndex="1" GridPane.columnSpan="3" GridPane.rowIndex="1">
                    <columnConstraints>
                      <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                    </columnConstraints>
                    <rowConstraints>
                      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                    </rowConstraints>
                     <children>
                        <TableView id="history" fx:id="refundTable" onMousePressed="#getRefundInfo" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/tableAlign.css">
                          <columnResizePolicy>
                                    <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
                                </columnResizePolicy>
                                  <columns>
                                  <TableColumn maxWidth="40" minWidth="40" prefWidth="40" />
                                  <TableColumn prefWidth="162.0" text="Refund ID">
                                    <cellValueFactory><PropertyValueFactory property="patientID" /></cellValueFactory>
                                  </TableColumn>    
                                  <TableColumn maxWidth="200.0" minWidth="200.0" prefWidth="200.0" text="Date">
                                    <cellValueFactory><PropertyValueFactory property="date" /></cellValueFactory>
                                  </TableColumn>
                                  <TableColumn prefWidth="75.0" text="Bill ID">  
                                     <cellValueFactory><PropertyValueFactory property="billID" /></cellValueFactory> 
                                  </TableColumn>  
                                  <TableColumn maxWidth="80.0" minWidth="80.0" text="Bill">
                                    <cellValueFactory><PropertyValueFactory property="bill" /></cellValueFactory>
                                  </TableColumn>
                                <TableColumn maxWidth="70.0" minWidth="70.0" prefWidth="70.0" text="Refund">
                                    <cellValueFactory><PropertyValueFactory property="refund" /></cellValueFactory>
                                </TableColumn> 
                          </columns>
                        </TableView>
                     </children>
                  </GridPane>
                  <Label fx:id="closeRefund" alignment="TOP_RIGHT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" onMousePressed="#closeRefundTable" GridPane.columnIndex="3" GridPane.columnSpan="2">
                     <GridPane.margin>
                        <Insets bottom="20.0" left="40.0" />
                     </GridPane.margin>
                     <cursor>
                        <Cursor fx:constant="HAND" />
                     </cursor>
                     <graphic>
                                             
                         <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="TIMES_CIRCLE" textFill="#333" />

                    </graphic>
                     <padding>
                        <Insets right="5.0" top="5.0" />
                     </padding>
                  </Label>
               </children>
            </GridPane>
         </children>
      </GridPane>
   </children>
</fx:root>
