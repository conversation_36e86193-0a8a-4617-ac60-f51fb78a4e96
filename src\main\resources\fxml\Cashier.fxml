<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.chart.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.effect.*?>
<?import javafx.scene.paint.*?>
<?import javafx.scene.text.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.web.*?>
<?import java.lang.*?>
<?import java.util.*?>
<?import javafx.scene.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import de.jensd.fx.glyphs.*?>
<?import de.jensd.fx.glyphs.materialicons.*?>
<?import de.jensd.fx.glyphs.fontawesome.*?>
<?import de.jensd.fx.glyphs.materialdesignicons.*?>
<?import de.jensd.fx.glyphs.octicons.*?>
<?import de.jensd.fx.glyphs.weathericons.*?>
<?import jfxtras.scene.control.*?>
<?import jfxtras.scene.control.agenda.*?>
<?import org.controlsfx.glyphfont.*?>
<?import impl.org.controlsfx.autocompletion.*?>
<?import javafx.collections.FXCollections?>
<?import javafx.scene.control.cell.PropertyValueFactory?>
<?import javafx.scene.control.cell.*?>
<?import javafx.collections.*?>
<?import Cashier.*?>

<fx:root id="background" prefHeight="646.0" prefWidth="900.0" type="javafx.scene.layout.AnchorPane" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
    <children>
        <TabPane layoutX="213.0" layoutY="60.0" prefHeight="200.0" prefWidth="200.0" side="LEFT" stylesheets="@../styles/tabbedPane.css" tabClosingPolicy="UNAVAILABLE" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="40.0">
        <tabs>
          <Tab text="Dashboard">   
            <graphic>
                
                    <FontAwesomeIconView fx:id="tab_ico0" glyphName="TACHOMETER" glyphStyle="" size="50px" textAlignment="LEFT" />
                  
            </graphic> 
            <content>
              <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                     <children>
                        <GridPane layoutX="14.0" layoutY="14.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="560.0" prefWidth="610.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                          <columnConstraints>
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="552.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="30.0" minWidth="30.0" prefWidth="30.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="250.0" minWidth="250.0" prefWidth="250.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                          </columnConstraints>
                          <rowConstraints>
                            <RowConstraints maxHeight="6.0" minHeight="6.0" prefHeight="6.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="60.0" minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="275.0" minHeight="49.0" prefHeight="275.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="1.7976931348623157E308" minHeight="10.0" prefHeight="308.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="20.0" minHeight="15.0" prefHeight="20.0" vgrow="SOMETIMES" />
                          </rowConstraints>
                           <children>
                              <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text=" Dashboard" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                <graphic>
                                    
                                    <Glyph fontFamily="FontAwesome" fontSize="30.0" icon="ARROW_CIRCLE_O_RIGHT" textFill="gray" />
                                      
                                </graphic>
                              </Label>
                              <Label id="box" alignment="TOP_LEFT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Payment History" GridPane.columnIndex="1" GridPane.columnSpan="3" GridPane.rowIndex="2" GridPane.rowSpan="2" />
                              <Label id="box" alignment="TOP_LEFT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="5" />
                              <Label id="box" alignment="TOP_LEFT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Pending Refunds" GridPane.columnIndex="3" GridPane.rowIndex="5" />
                              <TableView fx:id="billHistory" maxWidth="1.7976931348623157E308" onMouseClicked="#getSelectedRow" prefHeight="200.0" prefWidth="200.0" GridPane.columnIndex="1" GridPane.columnSpan="3" GridPane.rowIndex="3">
                                <columnResizePolicy>
                                    <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
                                </columnResizePolicy>
                                  <columns>
                                  <TableColumn maxWidth="40" minWidth="40" prefWidth="40" />
                                  <TableColumn maxWidth="500.0" minWidth="200.0" prefWidth="200.0" text="Patient ID">
                                    <cellValueFactory><PropertyValueFactory property="patientID" /></cellValueFactory>
                                  </TableColumn>    
                                  <TableColumn prefWidth="192.0" text="Date">
                                    <cellValueFactory><PropertyValueFactory property="date" /></cellValueFactory>
                                  </TableColumn>  
                                  <TableColumn prefWidth="102.0" text="Fees">
                                       <columns>  
                                          <TableColumn prefWidth="141.0" text="Doctor">
                                            <cellValueFactory>
                                                <PropertyValueFactory property="doctor" />
                                            </cellValueFactory>
                                          </TableColumn>
                                          <TableColumn prefWidth="75.0" text="Hospital">
                                          <cellValueFactory>
                                                <PropertyValueFactory property="hospital" />
                                            </cellValueFactory>
                                          </TableColumn>
                                          <TableColumn prefWidth="75.0" text="Pharmacy">
                                          <cellValueFactory>
                                                <PropertyValueFactory property="pharmacy" />
                                            </cellValueFactory>
                                          </TableColumn>    
                                          <TableColumn prefWidth="75.0" text="Laboratory">
                                          <cellValueFactory>
                                                <PropertyValueFactory property="laboratory" />
                                            </cellValueFactory>
                                          </TableColumn>   
                                          <TableColumn prefWidth="75.0" text="Appointment">
                                          <cellValueFactory>
                                                <PropertyValueFactory property="appointment" />
                                            </cellValueFactory>
                                          </TableColumn>  
                                       </columns>
                                  </TableColumn>
                                  <TableColumn prefWidth="192.0" text="Total">
                                    <cellValueFactory><PropertyValueFactory property="bill" /></cellValueFactory>
                                  </TableColumn>    
                                </columns>
                                 <GridPane.margin>
                                    <Insets bottom="55.0" left="10.0" right="10.0" />
                                 </GridPane.margin>
                              </TableView>
                              <AreaChart id="lineChart" fx:id="lineChart" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                <xAxis>
                                  <CategoryAxis side="BOTTOM" />
                                </xAxis>
                                <yAxis>
                                  <NumberAxis fx:id="yAxis" side="LEFT" />
                                </yAxis>
                                 <GridPane.margin>
                                    <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                                 </GridPane.margin>
                              </AreaChart>
                              <GridPane GridPane.columnIndex="3" GridPane.rowIndex="5">
                                <columnConstraints>
                                  <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                                </columnConstraints>
                                <rowConstraints>
                                  <RowConstraints maxHeight="1.7976931348623157E308" minHeight="80.0" prefHeight="80.0" vgrow="SOMETIMES" />
                                </rowConstraints>
                                 <GridPane.margin>
                                    <Insets top="20.0" />
                                 </GridPane.margin>
                                 <children>
                                    <Button id="transparentButton" fx:id="refundDetails" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" mnemonicParsing="false" onAction="#showRefundTable" stylesheets="@../styles/box.css" text="4">
                                       <font>
                                          <Font size="96.0" />
                                       </font>
                                       <cursor>
                                          <Cursor fx:constant="OPEN_HAND" />
                                       </cursor>
                                    </Button>
                                 </children>
                              </GridPane>
                              <Pagination fx:id="billHistoryPagination" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="200.0" prefWidth="200.0" GridPane.columnIndex="1" GridPane.columnSpan="3" GridPane.rowIndex="3">
                                 <GridPane.margin>
                                    <Insets bottom="10.0" left="10.0" right="10.0" />
                                 </GridPane.margin></Pagination>
                              
                           </children>
                        </GridPane>
                     </children>
              </AnchorPane>   
            </content>
          </Tab>
          <Tab text="Payments">
            <graphic>
                
                   <FontAwesomeIconView fx:id="tab_ico1" glyphName="MONEY" glyphStyle="" size="50px" textAlignment="LEFT" />
                   
            </graphic>  
            <content>
              <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                     <children>
                        <GridPane layoutX="62.0" layoutY="70.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="-0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                          <columnConstraints>
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="380.0" minWidth="10.0" prefWidth="195.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="30.0" minWidth="30.0" prefWidth="30.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="2000.0" minWidth="10.0" prefWidth="233.0" />
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                          </columnConstraints>
                          <rowConstraints>
                              <RowConstraints maxHeight="8.0" minHeight="8.0" prefHeight="8.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="80.0" minHeight="80.0" prefHeight="80.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="260.0" minHeight="260.0" prefHeight="260.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="30.0" minHeight="30.0" prefHeight="30.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="170.0" minHeight="150.0" prefHeight="170.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                          </rowConstraints>
                           <children>
                              <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Payements" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                <graphic>
                                    
                                    <Glyph fontFamily="FontAwesome" fontSize="30.0" icon="ARROW_CIRCLE_O_RIGHT" textFill="gray" />
                                      
                                </graphic>
                              </Label>
                              <Label id="box" alignment="TOP_LEFT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Services" GridPane.columnIndex="1" GridPane.rowIndex="2" GridPane.rowSpan="2">
                                 <font>
                                    <Font name="System Bold" size="13.0" />
                                 </font>
                              </Label>
                              <Label alignment="TOP_RIGHT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" GridPane.columnIndex="3" GridPane.rowIndex="2">
                                 <padding>
                                    <Insets right="5.0" top="5.0" />
                                 </padding>
                                 <graphic>
                                    
                                    <Glyph fontFamily="FontAwesome" fontSize="20.0" icon="MEDKIT" textFill="gray" />
                                      
                                </graphic>
                              </Label>
                              <Label id="box" alignment="TOP_LEFT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Summary" GridPane.columnIndex="1" GridPane.columnSpan="3" GridPane.rowIndex="5">
                                 <font>
                                    <Font name="System Bold" size="13.0" />
                                 </font></Label>
                              <GridPane GridPane.columnIndex="1" GridPane.columnSpan="3" GridPane.rowIndex="5">
                                <columnConstraints>
                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="-Infinity" prefWidth="200.0" />
                                    <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="-Infinity" prefWidth="200.0" />
                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="-Infinity" prefWidth="200.0" />
                                </columnConstraints>
                                <rowConstraints>
                                    <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                  <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                </rowConstraints>
                                 <children>
                                    <Label id="inputLabel11" maxHeight="30.0" maxWidth="120.0" minHeight="30.0" minWidth="0.0" stylesheets="@../styles/box.css" text="Date" GridPane.rowIndex="1">
                                       <GridPane.margin>
                                          <Insets left="40.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <Label id="inputLabel11" maxHeight="30.0" maxWidth="120.0" minHeight="30.0" minWidth="0.0" stylesheets="@../styles/box.css" text="Total" GridPane.rowIndex="2">
                                       <GridPane.margin>
                                          <Insets left="40.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <Label id="inputLabel11" maxHeight="30.0" maxWidth="120.0" minHeight="30.0" minWidth="0.0" stylesheets="@../styles/box.css" text="Method" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                       <GridPane.margin>
                                          <Insets left="40.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <Label id="inputLabel11" maxHeight="30.0" maxWidth="120.0" minHeight="30.0" minWidth="0.0" stylesheets="@../styles/box.css" text="Service Fees" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                       <GridPane.margin>
                                          <Insets left="40.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <Label id="inputLabel11" maxHeight="30.0" maxWidth="120.0" minHeight="30.0" minWidth="0.0" stylesheets="@../styles/box.css" text="VAT" GridPane.columnIndex="2" GridPane.rowIndex="1">
                                       <GridPane.margin>
                                          <Insets left="40.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <TextField id="inputText2" fx:id="billDate" maxHeight="30.0" maxWidth="1.7976931348623157E308" minHeight="30.0" minWidth="0.0" stylesheets="@../styles/box.css" GridPane.rowIndex="1">
                                       <GridPane.margin>
                                          <Insets left="160.0" right="10.0" />
                                       </GridPane.margin>
                                    </TextField>
                                    <TextField id="inputText2" fx:id="patientTotal" maxHeight="30.0" maxWidth="1.7976931348623157E308" minHeight="30.0" minWidth="0.0" stylesheets="@../styles/box.css" GridPane.rowIndex="2">
                                       <GridPane.margin>
                                          <Insets left="160.0" right="10.0" />
                                       </GridPane.margin>
                                    </TextField>
                                    <TextField id="inputText2" fx:id="serviceFees" maxHeight="30.0" minHeight="30.0" minWidth="0.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                       <GridPane.margin>
                                          <Insets left="160.0" right="10.0" />
                                       </GridPane.margin>
                                    </TextField>
                                    <TextField id="inputText2" fx:id="vat" maxHeight="30.0" maxWidth="1.7976931348623157E308" minHeight="30.0" minWidth="0.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="1">
                                       <GridPane.margin>
                                          <Insets left="160.0" right="10.0" />
                                       </GridPane.margin>
                                    </TextField>
                                    <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="0.0" stylesheets="@../styles/box.css" GridPane.rowIndex="1">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                       <graphic>
                                            
                                            <Glyph fontFamily="FontAwesome" fontSize="12" icon="CALENDAR" textFill="#333" />
                                              
                                        </graphic>
                                    </Label>
                                    <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="0.0" stylesheets="@../styles/box.css" GridPane.rowIndex="2">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                       <graphic>
                                            
                                            <Glyph fontFamily="FontAwesome" fontSize="15" icon="MONEY" textFill="#333" />
                                              
                                        </graphic>
                                    </Label>
                                    <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="0.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                       <graphic>
                                            
                                            <Glyph fontFamily="FontAwesome" fontSize="15" icon="CALCULATOR" textFill="#333" />
                                              
                                        </graphic>
                                    </Label>
                                    <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="0.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                       <graphic>
                                            
                                            <Glyph fontFamily="FontAwesome" fontSize="15" icon="CREDIT_CARD" textFill="#333" />
                                              
                                        </graphic>
                                    </Label>
                                    <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="0.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="1">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                       <graphic>
                                            
                                            <Glyph fontFamily="FontAwesome" fontSize="15" icon="CALCULATOR" textFill="#333" />
                                              
                                        </graphic>
                                    </Label>
                                    <GridPane GridPane.columnIndex="1" GridPane.columnSpan="2" GridPane.rowIndex="3">
                                      <columnConstraints>
                                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="100.0" />
                                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="220.0" minWidth="220.0" prefWidth="220.0" />
                                      </columnConstraints>
                                      <rowConstraints>
                                        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                      </rowConstraints>
                                       <children>
                                          <Button id="dark-blue" fx:id="issueButton" maxHeight="30.0" maxWidth="60.0" minHeight="30.0" minWidth="60.0" mnemonicParsing="false" onAction="#issueBill" stylesheets="@../styles/buttons.css" text="Issue" GridPane.columnIndex="1">
                                             <GridPane.margin>
                                                <Insets left="150.0" />
                                             </GridPane.margin>
                                          </Button>
                                          <Button id="dark-blue" fx:id="clearButton" maxHeight="30.0" maxWidth="40.0" minHeight="30.0" minWidth="40.0" mnemonicParsing="false" onAction="#clearBill" stylesheets="@../styles/buttons.css" GridPane.columnIndex="1">
                                             <GridPane.margin>
                                                <Insets left="100.0" />
                                             </GridPane.margin>
                                             <graphic>
                                    
                                                <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="ERASER" textFill="#eee" />

                                            </graphic>
                                          </Button>
                                       </children>
                                    </GridPane>
                                    <ComboBox id="inputText2" fx:id="paymentMethod" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                       <GridPane.margin>
                                          <Insets left="160.0" right="10.0" />
                                       </GridPane.margin>
                                       <items>
                                            <FXCollections fx:factory="observableArrayList">
                                           <String fx:value="Cash" />
                                           <String fx:value="Credit Card" />
                                           <String fx:value="Visa Card" />
                                            </FXCollections>
                                          </items>
                                    </ComboBox> 
                                 </children>
                              </GridPane>
                              <GridPane GridPane.columnIndex="1" GridPane.rowIndex="3">
                                <columnConstraints>
                                    <ColumnConstraints hgrow="SOMETIMES" maxWidth="40.0" minWidth="40.0" prefWidth="40.0" />
                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="120.0" minWidth="120.0" prefWidth="120.0" />
                                  <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                                </columnConstraints>
                                <rowConstraints>
                                    <RowConstraints maxHeight="30.0" minHeight="30.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="25.0" minHeight="25.0" prefHeight="25.0" vgrow="SOMETIMES" />
                                  <RowConstraints maxHeight="30.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="30.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="30.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                    <RowConstraints vgrow="SOMETIMES" />
                                  <RowConstraints maxHeight="70.0" minHeight="70.0" prefHeight="70.0" vgrow="SOMETIMES" />
                                </rowConstraints>
                                 <children>
                                    <Label id="inputLabel01" alignment="CENTER" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                       <graphic>
                                        
                                            <Glyph fontFamily="FontAwesome" fontSize="15" icon="USER" textFill="#333" />
                                        
                                        </graphic> 
                                    </Label>        
                                    <TextField id="inputText2" fx:id="patientID" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="2">
                                       <GridPane.margin>
                                          <Insets right="10.0" />
                                       </GridPane.margin>
                                    </TextField>
                                    <Button id="dark-blue" fx:id="searchBill" maxHeight="40.0" maxWidth="1.7976931348623157E308" minHeight="40.0" mnemonicParsing="false" onAction="#searchPatientBill" stylesheets="@../styles/buttons.css" text="Search Patient" GridPane.columnSpan="3" GridPane.rowIndex="6">
                                       <GridPane.margin>
                                          <Insets left="10.0" right="10.0" />
                                       </GridPane.margin>
                                    </Button>
                                    <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Appointment" GridPane.columnSpan="2" GridPane.rowIndex="2">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Pharmacy" GridPane.columnSpan="2" GridPane.rowIndex="3">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Laboratory" GridPane.columnSpan="2" GridPane.rowIndex="4">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <TextField id="inputText3" fx:id="appointmentTot" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="2">
                                       <GridPane.margin>
                                          <Insets right="10.0" />
                                       </GridPane.margin>
                                    </TextField>
                                    <TextField id="inputText3" fx:id="pharmacyTot" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="3">
                                       <GridPane.margin>
                                          <Insets right="10.0" />
                                       </GridPane.margin>
                                    </TextField>
                                    <TextField id="inputText3" fx:id="laboratoryTot" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="4">
                                       <GridPane.margin>
                                          <Insets right="10.0" />
                                       </GridPane.margin>
                                    </TextField>
                                    <ComboBox id="inputLabel11" fx:id="patientSearchType" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1">
                                        <items>
                                            <FXCollections fx:factory="observableArrayList">
                                                <String fx:value="Patient ID" />
                                                <String fx:value="Name" />
                                                <String fx:value="NIC" />
                                            </FXCollections>
                                        </items>
                                    </ComboBox>
                                 </children>
                              </GridPane>
                              <Label id="box" alignment="TOP_LEFT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Doctor Info" GridPane.columnIndex="3" GridPane.rowIndex="2" GridPane.rowSpan="2">
                                 <font>
                                    <Font name="System Bold" size="13.0" />
                                 </font>
                              </Label>
                              <GridPane GridPane.columnIndex="3" GridPane.rowIndex="3">
                                <columnConstraints>
                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="700.0" minWidth="10.0" prefWidth="200.0" />
                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="100.0" />
                                </columnConstraints>
                                <rowConstraints>
                                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                  <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                </rowConstraints>
                                 <children>
                                    <Label id="inputLabel11" maxHeight="30.0" maxWidth="100.0" minHeight="30.0" minWidth="100.0" stylesheets="@../styles/box.css" text="Patient Name">
                                       <GridPane.margin>
                                          <Insets left="40.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <TextField id="inputText2" fx:id="patientName" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css">
                                       <GridPane.margin>
                                          <Insets left="140.0" right="10.0" />
                                       </GridPane.margin>
                                       <opaqueInsets>
                                          <Insets />
                                       </opaqueInsets>
                                    </TextField>
                                    <TextField id="inputText2" fx:id="docName" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="3">
                                       <opaqueInsets>
                                          <Insets />
                                       </opaqueInsets>
                                       <GridPane.margin>
                                          <Insets left="140.0" right="10.0" />
                                       </GridPane.margin>
                                    </TextField>
                                    <Label id="inputLabel11" maxHeight="30.0" maxWidth="100.0" minHeight="30.0" minWidth="100.0" stylesheets="@../styles/box.css" text="Doctor Name" GridPane.rowIndex="3">
                                       <GridPane.margin>
                                          <Insets left="40.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <TextField id="inputText2" fx:id="docFee" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="4">
                                       <opaqueInsets>
                                          <Insets />
                                       </opaqueInsets>
                                       <GridPane.margin>
                                          <Insets left="140.0" right="10.0" />
                                       </GridPane.margin>
                                    </TextField>
                                    <Label id="inputLabel11" maxHeight="30.0" maxWidth="100.0" minHeight="30.0" minWidth="100.0" stylesheets="@../styles/box.css" text="Doctor Fee" GridPane.rowIndex="4">
                                       <GridPane.margin>
                                          <Insets left="40.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <TextField id="inputText2" fx:id="hospitalFee" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="5">
                                       <opaqueInsets>
                                          <Insets />
                                       </opaqueInsets>
                                       <GridPane.margin>
                                          <Insets left="140.0" right="10.0" />
                                       </GridPane.margin>
                                    </TextField>
                                    <Label id="inputLabel11" maxHeight="30.0" maxWidth="100.0" minHeight="30.0" minWidth="100.0" stylesheets="@../styles/box.css" text="Hospital Fee" GridPane.rowIndex="5">
                                       <GridPane.margin>
                                          <Insets left="40.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" stylesheets="@../styles/box.css">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                       <graphic>
                                        
                                            <Glyph fontFamily="FontAwesome" fontSize="15" icon="USER" textFill="#333" />
                                        
                                        </graphic> 
                                    </Label>
                                    <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="3">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                       <graphic>
                                        
                                            <Glyph fontFamily="FontAwesome" fontSize="15" icon="USER_MD" textFill="#333" />
                                        
                                        </graphic> 
                                    </Label>
                                    <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="4">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                       <graphic>
                                        
                                            <Glyph fontFamily="FontAwesome" fontSize="15" icon="MONEY" textFill="#333" />
                                        
                                        </graphic> 
                                    </Label>
                                    <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="5">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                       <graphic>
                                        
                                            <Glyph fontFamily="FontAwesome" fontSize="15" icon="H_SQUARE" textFill="#333" />
                                        
                                        </graphic> 
                                    </Label>
                                    <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="2">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                       <graphic>
                                        
                                            <Glyph fontFamily="FontAwesome" fontSize="15" icon="STETHOSCOPE" textFill="#333" />
                                        
                                        </graphic> 
                                    </Label>
                                    <Label id="inputLabel11" maxHeight="30.0" maxWidth="100.0" minHeight="30.0" minWidth="100.0" stylesheets="@../styles/box.css" text="Doctor ID" GridPane.rowIndex="2">
                                       <GridPane.margin>
                                          <Insets left="40.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <TextField id="inputText2" fx:id="docID" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="2">
                                       <opaqueInsets>
                                          <Insets />
                                       </opaqueInsets>
                                       <GridPane.margin>
                                          <Insets left="140.0" right="10.0" />
                                       </GridPane.margin>
                                    </TextField>
                                    <Label fx:id="ico" alignment="CENTER" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.rowSpan="6">
                                        <graphic>
                                        
                                            <Glyph fontFamily="FontAwesome" fontSize="120" icon="MEDKIT" textFill="#333" />
                                        
                                        </graphic>  
                                    </Label>    
                                 </children>
                              </GridPane>
                             
                           </children>
                        </GridPane>
                     </children></AnchorPane>
            </content>
          </Tab>
          <Tab text="Profile">
            <graphic>
                
                    <FontAwesomeIconView fx:id="tab_ico2" glyphName="USER" glyphStyle="" size="50px" textAlignment="LEFT" />
                    
            </graphic>  
            <content>
              <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                     <children>
                        <GridPane layoutX="83.0" layoutY="77.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                          <columnConstraints>
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="200.0" minWidth="200.0" prefWidth="200.0" />
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="50.0" prefWidth="600.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="50.0" prefWidth="50.0" />
                          </columnConstraints>
                          <rowConstraints>
                            <RowConstraints maxHeight="8.0" minHeight="8.0" prefHeight="8.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="80.0" minHeight="80.0" prefHeight="80.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="100000.0" minHeight="200.0" prefHeight="200.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                          </rowConstraints>
                           <children>
                              <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Profile" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                <graphic>  
                                     
                                    <Glyph fontFamily="FontAwesome" fontSize="30.0" icon="ARROW_CIRCLE_O_RIGHT" textFill="gray" />
                                    
                                </graphic>
                              </Label>
                              <TabPane maxHeight="1000.0" maxWidth="1000.0" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/tabbedPane3.css" tabClosingPolicy="UNAVAILABLE" GridPane.columnIndex="2" GridPane.rowIndex="2">
                                <tabs>
                                  <Tab text="Basic Details">
                                    <content>
                                      <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                             <children>
                                                <GridPane layoutX="66.0" layoutY="69.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                                  <columnConstraints>
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                                                    <ColumnConstraints hgrow="SOMETIMES" maxWidth="450.0" minWidth="10.0" prefWidth="228.0" />
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="50.0" prefWidth="100.0" />
                                                  </columnConstraints>
                                                  <rowConstraints>
                                                      <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                    <RowConstraints maxHeight="220.0" minHeight="220.0" prefHeight="220.0" vgrow="SOMETIMES" />
                                                    <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                    <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="1.7976931348623157E308" minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                                                  </rowConstraints>
                                                   <children>
                                                      <GridPane prefHeight="212.0" prefWidth="279.0" GridPane.columnIndex="1" GridPane.rowIndex="1" GridPane.rowSpan="7">
                                                        <columnConstraints>
                                                          <ColumnConstraints hgrow="SOMETIMES" maxWidth="206.0" minWidth="10.0" prefWidth="149.0" />
                                                          <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="136.0" />
                                                        </columnConstraints>
                                                        <rowConstraints>
                                                            <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                                                          <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                          <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                          <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="1.7976931348623157E308" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                        </rowConstraints>
                                                         <children>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Name" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" prefHeight="16.0" prefWidth="86.0" stylesheets="@../styles/box.css" text="Date Of Birth" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="NIC" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Age" GridPane.rowIndex="4">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Gender" GridPane.rowIndex="5">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Nationality" GridPane.rowIndex="6">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Mobile" GridPane.rowIndex="8">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Email" GridPane.rowIndex="9">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Address" GridPane.rowIndex="10">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Religion" GridPane.rowIndex="7">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <TextField id="inputText1" fx:id="cashierName" disable="true" maxHeight="30.0" maxWidth="650.0" minHeight="30.0" prefWidth="500.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                                                            <TextField id="inputText1" fx:id="cashierNIC" disable="true" maxHeight="30.0" maxWidth="650.0" minHeight="30.0" prefWidth="500.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                                                            <TextField id="inputText1" fx:id="cashierAge" disable="true" maxHeight="30.0" maxWidth="650.0" minHeight="30.0" prefWidth="500.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="4" />
                                                            <TextField id="inputText1" fx:id="cashierNationality" disable="true" maxHeight="30.0" maxWidth="650.0" minHeight="30.0" prefWidth="500.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="6" />
                                                            <TextField id="inputText1" fx:id="cashierReligion" disable="true" maxHeight="30.0" maxWidth="650.0" minHeight="30.0" prefWidth="500.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="7" />
                                                            <TextField id="inputText1" fx:id="cashierMobile" disable="true" maxHeight="30.0" maxWidth="650.0" minHeight="30.0" prefWidth="500.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="8" />
                                                            <TextField id="inputText1" fx:id="cashierEmail" disable="true" maxHeight="30.0" maxWidth="650.0" minHeight="30.0" prefWidth="500.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="9" />
                                                            <TextField id="inputText1" fx:id="cashierAddress" disable="true" maxHeight="30.0" maxWidth="650.0" minHeight="30.0" prefWidth="500.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="10" />
                                                            <DatePicker id="inputText1" fx:id="cashierDOB" disable="true" maxHeight="30.0" maxWidth="650.0" minHeight="30.0" prefWidth="500.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                                                            <ComboBox id="inputText2" fx:id="cashierGender" disable="true" maxHeight="30.0" maxWidth="650.0" minWidth="30.0" prefWidth="500.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                                                <items>
                                                                    <FXCollections fx:factory="observableArrayList">
                                                                   
                                                                    </FXCollections>
                                                                </items>
                                                            </ComboBox> 
                                                                
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="1">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="USER" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="2">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="CERTIFICATE" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="3">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="CALENDAR" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="4">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="STREET_VIEW" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="5">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="MALE" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="6">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="BELL" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="7">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                                <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="BULLSEYE" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="8">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="MOBILE_PHONE" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="9">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="ENVELOPE" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="10">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                                <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="MAP_MARKER" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Button id="dark-blue" fx:id="editBasicInfoButton" maxHeight="30.0" maxWidth="60.0" minHeight="30.0" minWidth="60.0" mnemonicParsing="false" onAction="#editBasicInfo" stylesheets="@../styles/buttons.css" text="Edit" GridPane.rowIndex="11">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Button>
                                                         </children>
                                                      </GridPane>
                                                   </children>
                                                </GridPane>
                                             </children></AnchorPane>
                                    </content>
                                  </Tab>
                                  <Tab text="Account">
                                    <content>
                                      <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                             <children>
                                                <GridPane layoutX="43.0" layoutY="72.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                                  <columnConstraints>
                                                    <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="170.0" minWidth="170.0" prefWidth="170.0" />
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="300.0" minWidth="10.0" prefWidth="300.0" />
                                                    <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="50.0" prefWidth="100.0" />
                                                  </columnConstraints>
                                                  <rowConstraints>
                                                    <RowConstraints maxHeight="60.0" minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
                                                    <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                    <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                  </rowConstraints>
                                                   <children>
                                                      <Label id="inputLabel1" maxHeight="30.0" maxWidth="130.0" minHeight="30.0" minWidth="130.0" stylesheets="@../styles/box.css" text="User Name" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="30.0" maxWidth="130.0" minHeight="30.0" minWidth="130.0" stylesheets="@../styles/box.css" text="User Type" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="30.0" maxWidth="130.0" minHeight="30.0" minWidth="130.0" stylesheets="@../styles/box.css" text="User ID" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <TextField id="inputText1" fx:id="cashierUserName" disable="true" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                                                      <TextField id="inputText1" fx:id="cashierUserType" disable="true" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="2" />
                                                      <TextField id="inputText1" fx:id="cashierUserID" disable="true" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="3" />
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                         <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="USER" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                         <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="USERS" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                         <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="BULLSEYE" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="8">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                         <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="KEY" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="6">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                         <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="UNLOCK_ALT" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="7">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                         <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="UNLOCK" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <TextField id="inputText1" fx:id="cashierConfirmPassword" disable="true" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="8" />
                                                      <TextField id="inputText1" fx:id="cashierNewPassword" disable="true" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="7" />
                                                      <TextField id="inputText1" fx:id="cashierPassword" disable="true" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="6" />
                                                      <Label id="inputLabel1" maxHeight="30.0" maxWidth="130.0" minHeight="30.0" minWidth="130.0" stylesheets="@../styles/box.css" text="Confirm Password" GridPane.columnIndex="1" GridPane.rowIndex="8">
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="30.0" maxWidth="130.0" minHeight="30.0" minWidth="130.0" stylesheets="@../styles/box.css" text="new Password" GridPane.columnIndex="1" GridPane.rowIndex="7">
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="30.0" maxWidth="130.0" minHeight="30.0" minWidth="130.0" stylesheets="@../styles/box.css" text="Current Password" GridPane.columnIndex="1" GridPane.rowIndex="6">
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Button id="dark-blue" fx:id="editUserInfoButton" maxHeight="30.0" maxWidth="60.0" minHeight="30.0" minWidth="60.0" mnemonicParsing="false" onAction="#editUserInfo" stylesheets="@../styles/buttons.css" text="Edit" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                      </Button>
                                                      <Button id="dark-blue" fx:id="editPasswordInfoButton" maxHeight="30.0" maxWidth="60.0" minHeight="30.0" minWidth="60.0" mnemonicParsing="false" onAction="#editPasswordInfo" stylesheets="@../styles/buttons.css" text="Edit" GridPane.columnIndex="1" GridPane.rowIndex="9">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                      </Button>
                                                   </children>
                                                </GridPane>
                                             </children></AnchorPane>
                                    </content>
                                  </Tab>
                                </tabs>
                              </TabPane>
                              <GridPane GridPane.columnIndex="1" GridPane.rowIndex="2">
                                <columnConstraints>
                                  <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                                </columnConstraints>
                                <rowConstraints>
                                  <RowConstraints maxHeight="39.0" minHeight="39.0" prefHeight="39.0" vgrow="SOMETIMES" />
                                  <RowConstraints maxHeight="275.0" minHeight="275.0" prefHeight="275.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                  <RowConstraints maxHeight="572.0" minHeight="10.0" prefHeight="167.0" vgrow="SOMETIMES" />
                                    <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                </rowConstraints>
                                 <children>
                                    <Label id="profileBox" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.rowIndex="1" GridPane.rowSpan="4" />
                                    <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/header.css" />
                                    <ImageView fx:id="profileImage" fitHeight="180.0" fitWidth="180.0" pickOnBounds="true" preserveRatio="true" GridPane.rowIndex="1">
                                       <GridPane.margin>
                                          <Insets left="10.0" top="60.0" />
                                       </GridPane.margin>
                                       <image>
                                          <Image url="@../imgs/profile.png" />
                                       </image>
                                    </ImageView>
                                    <Button id="dark-blue" fx:id="editProfilePicButton" maxHeight="30.0" maxWidth="1.7976931348623157E308" minHeight="30.0" minWidth="65.0" mnemonicParsing="false" onAction="#editProfilePic" stylesheets="@../styles/buttons.css" text="Edit" GridPane.rowIndex="2">
                                       <GridPane.margin>
                                          <Insets left="10.0" right="10.0" />
                                       </GridPane.margin>
                                    </Button>
                                 </children>
                              </GridPane>
                           </children>
                        </GridPane>
                     </children></AnchorPane>
            </content>
          </Tab>
        </tabs>
      </TabPane>
      <GridPane layoutX="22.0" layoutY="5.0" maxHeight="51.0" maxWidth="1.7976931348623157E308" prefHeight="51.0" prefWidth="778.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
        <columnConstraints>
          <ColumnConstraints hgrow="SOMETIMES" maxWidth="170.0" minWidth="170.0" prefWidth="170.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="257.0" minWidth="10.0" prefWidth="139.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="287.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="60.0" minWidth="60.0" prefWidth="60.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="60.0" minWidth="60.0" prefWidth="60.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="60.0" minWidth="60.0" prefWidth="60.0" />
          <ColumnConstraints hgrow="SOMETIMES" maxWidth="20.0" minWidth="8.0" prefWidth="20.0" />
        </columnConstraints>
        <rowConstraints>
          <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
        </rowConstraints>
         <children>
            <Label alignment="BASELINE_CENTER" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="40.0" prefWidth="78.0" stylesheets="@../styles/header.css" GridPane.columnIndex="3">    
            
            </Label>
            <Label alignment="BASELINE_CENTER" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" minWidth="-Infinity" stylesheets="@../styles/header.css" GridPane.columnIndex="4">
             
            </Label>    
            
            <Label alignment="BASELINE_CENTER" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" minWidth="-Infinity" stylesheets="@../styles/header.css" textFill="#e1dede" GridPane.columnIndex="5">
             
            </Label>
            <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/header.css" GridPane.columnIndex="1" />
            <Label alignment="CENTER" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="51.0" prefWidth="233.0" stylesheets="@../styles/header.css" text=" HealthPlus" textFill="#d7d6d6">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
               <graphic>
               
                   <FontAwesomeIconView fill="#ff6666" glyphName="PLUS_CIRCLE" glyphStyle="" size="20px" />
                 
               </graphic>
               <effect>
                  <Bloom threshold="0.84" />
               </effect> 
            </Label>
            <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="55.0" prefWidth="248.0" stylesheets="@../styles/header.css" GridPane.columnIndex="2" />
            <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/header.css" GridPane.columnIndex="6" />
            <Button id="logout" fx:id="logoutButton" maxWidth="40.0" minWidth="40.0" mnemonicParsing="false" onAction="#logout" stylesheets="@../styles/box.css" GridPane.columnIndex="5">
               <GridPane.margin>
                  <Insets left="10.0" />
               </GridPane.margin>
               <graphic>
                    
                   <FontAwesomeIconView fill="#ffffff" glyphName="SIGN_OUT" glyphStyle="" size="15px" />
                      
               </graphic>
               <cursor>
                  <Cursor fx:constant="HAND" />
               </cursor>
            </Button>
            <Button id="iconButton" fx:id="AllMessages" maxWidth="40.0" minWidth="40.0" mnemonicParsing="false" onAction="#showAllMessages" stylesheets="@../styles/box.css" GridPane.columnIndex="4">
               <graphic>
                    
                  <FontAwesomeIconView fill="#222" glyphName="ENVELOPE" glyphStyle="" size="15px" />
                   
                </graphic>
                <GridPane.margin>
                  <Insets left="10.0" />
               </GridPane.margin>
               <cursor>
                  <Cursor fx:constant="HAND" />
               </cursor>
            </Button>
            <Button id="iconButton" fx:id="showUserButton" maxWidth="40.0" minWidth="40.0" mnemonicParsing="false" onAction="#showUser" stylesheets="@../styles/box.css" GridPane.columnIndex="3">
               <GridPane.margin>
                  <Insets left="10.0" />
               </GridPane.margin>
                <graphic>
                    
                    <FontAwesomeIconView fill="#222" glyphName="USER" glyphStyle="" size="15px" />
                      
                </graphic>
               <cursor>
                  <Cursor fx:constant="HAND" />
               </cursor>
               
            </Button>
            </children>
      </GridPane>    
    </children>
</fx:root>
