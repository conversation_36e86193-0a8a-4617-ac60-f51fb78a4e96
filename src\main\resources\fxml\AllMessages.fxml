<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.chart.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.effect.*?>
<?import javafx.scene.paint.*?>
<?import javafx.scene.text.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.web.*?>
<?import java.lang.*?>
<?import java.util.*?>
<?import javafx.scene.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import de.jensd.fx.glyphs.*?>
<?import de.jensd.fx.glyphs.materialicons.*?>
<?import de.jensd.fx.glyphs.fontawesome.*?>
<?import de.jensd.fx.glyphs.materialdesignicons.*?>
<?import de.jensd.fx.glyphs.octicons.*?>
<?import de.jensd.fx.glyphs.weathericons.*?>
<?import jfxtras.scene.control.*?>
<?import jfxtras.scene.control.agenda.*?>
<?import org.controlsfx.glyphfont.*?>
<?import impl.org.controlsfx.autocompletion.*?>
<?import javafx.collections.FXCollections?>
<?import javafx.scene.control.cell.PropertyValueFactory?>
<?import javafx.scene.control.cell.*?>
<?import javafx.collections.*?>

<fx:root id="anchor" stylesheets="@../styles/popup.css" type="javafx.scene.layout.AnchorPane" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">   <children>
      <Label layoutX="145.0" layoutY="251.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0" />
      <GridPane layoutX="129.0" layoutY="158.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
        <columnConstraints>
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="350.0" minWidth="350.0" prefWidth="350.0" />
        </columnConstraints>
        <rowConstraints>
          <RowConstraints maxHeight="240.0" minHeight="240.0" prefHeight="240.0" vgrow="SOMETIMES" />
        </rowConstraints>
         <children>
            <Label id="box4" alignment="TOP_RIGHT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/popup.css">
                
            </Label>
            <Button id="dark-blue" fx:id="newMessageButton" maxHeight="30.0" maxWidth="130.0" mnemonicParsing="false" onAction="#newMessage" stylesheets="@../styles/buttons.css" text="  New Message">
               <GridPane.margin>
                  <Insets left="210.0" top="180.0" />
               </GridPane.margin>
               <graphic>  
                                    
                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="PENCIL" textFill="#eee" />

                </graphic>
            </Button>
            <TableView fx:id="messagesTable" onMousePressed="#showMessage" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/tableHeader.css">
               <GridPane.margin>
                  <Insets bottom="60.0" left="10.0" right="10.0" top="20.0" />
               </GridPane.margin>
            </TableView>
         </children>
      </GridPane>
   </children>
</fx:root>
