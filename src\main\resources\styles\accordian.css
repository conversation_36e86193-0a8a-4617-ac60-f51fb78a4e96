.accordion .titled-pane .title {
    /*-fx-background-color:linear-gradient(#aaa 0%, #fff 100%); /*linear-gradient(#131313 0%, #424141 100%); /*transparent ;*/
    -fx-text-fill: #d9d9d9;
    -fx-alignment: CENTER;
}


.titled-pane {
    -fx-text-fill: #777;
}

.titled-pane > .title 
{
-fx-background-color: -fx-box-border, -fx-inner-border, -fx-body-color;
-fx-background-insets: 0, 1, 2;
-fx-background-radius: 0 0 0 0, 0 0 0 0, 0 0 0 0;
-fx-padding: 0.166667em 0.833333em 0.25em 0.833333em; /* 2 10 3 10 */
}

.titled-pane:focused > .title 
{
-fx-color: -fx-focus-color;
-fx-font-weight:bold;
}

.titled-pane:expanded > .title 
{
-fx-background-color:linear-gradient(#131313 0%, #424141 100%);
-fx-color: -fx-focus-color;
-fx-font-weight:bold;
-fx-text-fill:#fff !important;
}

.titled-pane:collapsed > .title 
{
    /*-fx-background-color:linear-gradient(#aaa 0%, #fff 100%);*/
}

.titled-pane > .title > .arrow-button 
{
-fx-background-color: null;
-fx-background-insets: 0;
-fx-background-radius: 0;
-fx-padding: 0.0em 0.25em 0.0em 0.0em; /* 0 3 0 0 */
}

.titled-pane > .title > .arrow-button .arrow 
{
-fx-background-color: -fx-mark-highlight-color, -fx-mark-color;
-fx-background-insets: 1 0 -1 0, 0;
-fx-padding: 0.25em 0.3125em 0.25em 0.3125em; /* 3 3.75 3 3.75 */
-fx-shape: "M 0 0 h 7 l -3.5 4 z";
}

.titled-pane:collapsed > .title > .arrow-button .arrow 
{
-fx-rotate: -90;
}

.titled-pane > *.content 
{
-fx-background-color:
-fx-box-border,
linear-gradient(to bottom, derive(-fx-color,-02%), derive(-fx-color,65%) 12%,      derive(-fx-color,23%) 88%, derive(-fx-color,50%) 99%, -fx-box-border);
-fx-background-insets: 0, 0 1 1 1;
-fx-padding: 0.167em;
-fx-background-radius: 0 0 5 5, 0 0 4 4, 0 0 3 3;
-fx-border-radius: 0 0 5 5;
-fx-background-insets: 0, 1, 2;
 }

.titled-pane:focused > .title > .arrow-button .arrow 
{
-fx-background-color: #0798bc;
}

.titled-pane:focused 
{ 
-fx-text-fill: white;
}
