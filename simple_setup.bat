@echo off
echo Simple HealthPlus Database Setup
echo =================================
echo.
echo This script will try different common MySQL passwords
echo.

echo Trying with empty password...
mysql -u root -e "CREATE DATABASE IF NOT EXISTS test_HMS2;" 2>nul
if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: Connected with empty password!
    goto :setup
)

echo Trying with password 'root'...
mysql -u root -proot -e "CREATE DATABASE IF NOT EXISTS test_HMS2;" 2>nul
if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: Connected with password 'root'!
    set MYSQL_PASS=root
    goto :setup
)

echo Trying with password 'password'...
mysql -u root -ppassword -e "CREATE DATABASE IF NOT EXISTS test_HMS2;" 2>nul
if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: Connected with password 'password'!
    set MYSQL_PASS=password
    goto :setup
)

echo Trying with password 'admin'...
mysql -u root -padmin -e "CREATE DATABASE IF NOT EXISTS test_HMS2;" 2>nul
if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: Connected with password 'admin'!
    set MYSQL_PASS=admin
    goto :setup
)

echo Could not connect with common passwords.
echo Please check your MySQL installation or reset the root password.
pause
exit /b 1

:setup
echo.
echo Setting up database...
if defined MYSQL_PASS (
    mysql -u root -p%MYSQL_PASS% -e "CREATE USER IF NOT EXISTS 'heshan'@'localhost' IDENTIFIED BY 'pass'; GRANT ALL PRIVILEGES ON test_HMS2.* TO 'heshan'@'localhost'; FLUSH PRIVILEGES;"
    mysql -u root -p%MYSQL_PASS% test_HMS2 < database\hms_db.sql
    mysql -u root -p%MYSQL_PASS% -e "USE test_HMS2; SELECT user_name, password, user_type FROM sys_user LIMIT 5;"
) else (
    mysql -u root -e "CREATE USER IF NOT EXISTS 'heshan'@'localhost' IDENTIFIED BY 'pass'; GRANT ALL PRIVILEGES ON test_HMS2.* TO 'heshan'@'localhost'; FLUSH PRIVILEGES;"
    mysql -u root test_HMS2 < database\hms_db.sql
    mysql -u root -e "USE test_HMS2; SELECT user_name, password, user_type FROM sys_user LIMIT 5;"
)

echo.
echo Setup complete! Try logging in with:
echo Username: user021, Password: 1234 (Admin)
echo Username: user001, Password: 1234 (Doctor)
pause
