<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.text.*?>
<?import javafx.scene.control.*?>
<?import java.lang.*?>
<?import javafx.scene.layout.*?>
<?import de.jensd.fx.glyphs.fontawesome.*?>

<fx:root id="background" fx:id="anchorPane" prefHeight="60.0" prefWidth="220.0" type="javafx.scene.layout.AnchorPane" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
    <children>
      <Label id="border" layoutX="33.0" layoutY="33.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/warning.css" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0" />
      <Label id="iconBox" alignment="CENTER" layoutX="14.0" layoutY="23.0" maxWidth="50.0" minWidth="50.0" stylesheets="@../styles/warning.css" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.topAnchor="0.0">
      <graphic>
                    
          <FontAwesomeIconView fill="#fff" glyphName="EXCLAMATION_TRIANGLE" glyphStyle="" size="25px" />

        </graphic>
      </Label>
      <Label layoutX="91.0" layoutY="23.0" text="Invalid!" AnchorPane.bottomAnchor="30.0" AnchorPane.leftAnchor="60.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
         <font>
            <Font name="System Bold" size="13.0" />
         </font>
      </Label>
      <Label fx:id="formatLabel" layoutX="130.0" layoutY="39.0" text="Format : format" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="60.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="20.0" />     
   </children>
</fx:root>
