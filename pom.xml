<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.hms</groupId>
    <artifactId>hms_test_2</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>HealthPlus</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <mainClass>com.hms.hms_test_2.MainApp</mainClass>
        <javafx.version>17.0.10</javafx.version>
    </properties>

    <organization>
        <name>HealthPlus</name>
    </organization>

    <build>
        <plugins>
            <!-- JavaFX Maven Plugin -->
            <plugin>
                <groupId>org.openjfx</groupId>
                <artifactId>javafx-maven-plugin</artifactId>
                <version>0.0.8</version>
                <configuration>
                    <mainClass>com.hms.hms_test_2.MainApp</mainClass>
                    <options>
                        <option>--add-opens</option>
                        <option>javafx.base/com.sun.javafx.event=org.controlsfx.controls</option>
                        <option>--add-opens</option>
                        <option>javafx.graphics/com.sun.javafx.scene=org.controlsfx.controls</option>
                        <option>--add-opens</option>
                        <option>javafx.base/com.sun.javafx.event=ALL-UNNAMED</option>
                        <option>--add-exports</option>
                        <option>javafx.base/com.sun.javafx.event=ALL-UNNAMED</option>
                    </options>
                </configuration>
            </plugin>

            <!-- Maven Compiler Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>

            <!-- Maven Surefire Plugin for JavaFX Runtime Arguments -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <argLine>
                        --module-path ${java.home}/lib --add-modules javafx.controls,javafx.fxml
                    </argLine>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-controls</artifactId>
            <version>${javafx.version}</version>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-fxml</artifactId>
            <version>${javafx.version}</version>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-base</artifactId>
            <version>${javafx.version}</version>
        </dependency>

        <dependency>
            <groupId>de.jensd</groupId>
            <artifactId>fontawesomefx-fontawesome</artifactId>
            <version>4.7.0-9.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.controlsfx</groupId>
            <artifactId>controlsfx</artifactId>
            <version>11.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.jfxtras</groupId>
            <artifactId>jfxtras-common</artifactId>
            <version>8.0-r5</version>
        </dependency>
        <dependency>
            <groupId>org.jfxtras</groupId>
            <artifactId>jfxtras-fxml</artifactId>
            <version>8.0-r5</version>
        </dependency>
        <dependency>
            <groupId>org.jfxtras</groupId>
            <artifactId>jfxtras-controls</artifactId>
            <version>8.0-r5</version>
        </dependency>
        <dependency>
            <groupId>org.jfxtras</groupId>
            <artifactId>jfxtras-window</artifactId>
            <version>8.0-r5</version>
        </dependency>
        <dependency>
            <groupId>org.jfxtras</groupId>
            <artifactId>jfxtras-menu</artifactId>
            <version>8.0-r5</version>
        </dependency>
        <dependency>
            <groupId>org.jfxtras</groupId>
            <artifactId>jfxtras-labs</artifactId>
            <version>8.0-r5</version>
        </dependency>
        <dependency>
            <groupId>org.mariadb.jdbc</groupId>
            <artifactId>mariadb-java-client</artifactId>
            <version>1.1.7</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-core</artifactId>
            <version>1.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.maven.shared</groupId>
            <artifactId>maven-shared-utils</artifactId>
            <version>3.3.4</version>
            <type>jar</type>
        </dependency>
    </dependencies>
</project>
