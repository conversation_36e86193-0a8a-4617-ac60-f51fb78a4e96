<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.chart.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.effect.*?>
<?import javafx.scene.paint.*?>
<?import javafx.scene.text.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.web.*?>
<?import java.lang.*?>
<?import java.util.*?>
<?import javafx.scene.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import de.jensd.fx.glyphs.*?>
<?import de.jensd.fx.glyphs.materialicons.*?>
<?import de.jensd.fx.glyphs.fontawesome.*?>
<?import de.jensd.fx.glyphs.materialdesignicons.*?>
<?import de.jensd.fx.glyphs.octicons.*?>
<?import de.jensd.fx.glyphs.weathericons.*?>
<?import jfxtras.scene.control.*?>
<?import jfxtras.scene.control.agenda.*?>
<?import org.controlsfx.glyphfont.*?>
<?import impl.org.controlsfx.autocompletion.*?>
<?import javafx.collections.FXCollections?>
<?import javafx.scene.control.cell.PropertyValueFactory?>
<?import javafx.scene.control.cell.*?>
<?import javafx.collections.*?>
<?import Pharmacist.*?>

<fx:root id="anchor" stylesheets="@../styles/popup.css" type="javafx.scene.layout.AnchorPane" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">   <children>
      <Label layoutX="145.0" layoutY="251.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0" />
      <GridPane layoutX="129.0" layoutY="158.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
        <columnConstraints>
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="20.0" prefWidth="50.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="600.0" minWidth="600.0" prefWidth="600.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="20.0" prefWidth="50.0" />
        </columnConstraints>
        <rowConstraints>
            <RowConstraints maxHeight="1.7976931348623157E308" minHeight="20.0" vgrow="SOMETIMES" />
          <RowConstraints maxHeight="200.0" minHeight="200.0" prefHeight="200.0" vgrow="SOMETIMES" />
            <RowConstraints maxHeight="1.7976931348623157E308" minHeight="20.0" vgrow="SOMETIMES" />
        </rowConstraints>
         <children>
            <Label id="box" alignment="TOP_RIGHT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/popup.css" GridPane.columnIndex="1" GridPane.rowIndex="1">
                
            </Label>
            <TableView fx:id="prescList" onMousePressed="#LoadPrescriptionInfo" prefHeight="200.0" prefWidth="200.0" GridPane.columnIndex="1" GridPane.rowIndex="1">
              <columnResizePolicy>
                    <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
              </columnResizePolicy>  
              <columns>
                <TableColumn prefWidth="173.0" text="Prescription ID">
                    <cellValueFactory>
                        <PropertyValueFactory property="prescID" />
                    </cellValueFactory>
                </TableColumn>    
                <TableColumn minWidth="2.0" prefWidth="239.0" text="Date">
                    <cellValueFactory>
                        <PropertyValueFactory property="date" />
                    </cellValueFactory>
                </TableColumn> 
                <TableColumn prefWidth="170.0" text="Consultant">
                    <cellValueFactory>
                        <PropertyValueFactory property="doctor" />
                    </cellValueFactory>
                </TableColumn> 
              </columns>
               <GridPane.margin>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="25.0" />
               </GridPane.margin>
            </TableView>
            <Button id="transparentButton2" maxHeight="30.0" maxWidth="30.0" mnemonicParsing="false" onAction="#close" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="1">
               <GridPane.margin>
                  <Insets bottom="180.0" left="580.0" />
               </GridPane.margin>
               <graphic>             
                        <Glyph fontFamily="FontAwesome" fontSize="15" icon="TIMES" textFill="#333333" />
                </graphic>
            </Button>
         </children>
      </GridPane>
   </children>
</fx:root>
