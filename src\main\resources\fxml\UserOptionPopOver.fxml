<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.chart.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.effect.*?>
<?import javafx.scene.paint.*?>
<?import javafx.scene.text.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.web.*?>
<?import java.lang.*?>
<?import java.util.*?>
<?import javafx.scene.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import de.jensd.fx.glyphs.*?>
<?import de.jensd.fx.glyphs.materialicons.*?>
<?import de.jensd.fx.glyphs.fontawesome.*?>
<?import de.jensd.fx.glyphs.materialdesignicons.*?>
<?import de.jensd.fx.glyphs.octicons.*?>
<?import de.jensd.fx.glyphs.weathericons.*?>
<?import jfxtras.scene.control.*?>
<?import jfxtras.scene.control.agenda.*?>
<?import org.controlsfx.glyphfont.*?>
<?import impl.org.controlsfx.autocompletion.*?>
<?import javafx.collections.FXCollections?>
<?import javafx.scene.control.cell.PropertyValueFactory?>
<?import javafx.scene.control.cell.*?>
<?import javafx.collections.*?>
<?import Pharmacist.*?>

<fx:root maxHeight="1.7976931348623157E308" maxWidth="260.0" minHeight="10.0" minWidth="10.0" prefHeight="240.0" prefWidth="257.0" type="javafx.scene.layout.AnchorPane" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
   <children>
      <GridPane layoutX="14.0" layoutY="27.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
        <columnConstraints>
          <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="100.0" prefWidth="50.0" />
        </columnConstraints>
        <rowConstraints>
          <RowConstraints maxHeight="10.0" minHeight="10.0" prefHeight="10.0" vgrow="SOMETIMES" />
          <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
          <RowConstraints maxHeight="1.7976931348623157E308" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
            <RowConstraints maxHeight="10.0" minHeight="10.0" prefHeight="10.0" vgrow="SOMETIMES" />
        </rowConstraints>
         <children>
            <Label maxHeight="30.0" maxWidth="1.7976931348623157E308" prefHeight="27.0" prefWidth="106.0" text="Online : 2" GridPane.rowIndex="1">
               <GridPane.margin>
                  <Insets left="65.0" />
               </GridPane.margin>
            </Label>
            <AreaChart fx:id="onlineChart" title="Last 2 Hours" GridPane.rowIndex="2">
              <xAxis>
                <CategoryAxis side="BOTTOM" />
              </xAxis>
              <yAxis>
                <NumberAxis side="LEFT" />
              </yAxis>
               <GridPane.margin>
                  <Insets />
               </GridPane.margin>
            </AreaChart>
            <Label maxHeight="30.0" alignment="CENTER" id="iconCircle2" maxWidth="30.0" GridPane.rowIndex="1" stylesheets="@../styles/box.css">
               <GridPane.margin>
                  <Insets left="20.0" />
               </GridPane.margin>
               <graphic>
                    <FontAwesomeIconView glyphName="CHECK" size="18px" glyphStyle="" fill="#fff"/>
                </graphic>
            </Label>
         </children>
      </GridPane>
   </children>
</fx:root>
