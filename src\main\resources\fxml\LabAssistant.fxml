<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.chart.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.effect.*?>
<?import javafx.scene.paint.*?>
<?import javafx.scene.text.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.web.*?>
<?import java.lang.*?>
<?import java.util.*?>
<?import javafx.scene.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import de.jensd.fx.glyphs.*?>
<?import de.jensd.fx.glyphs.materialicons.*?>
<?import de.jensd.fx.glyphs.fontawesome.*?>
<?import de.jensd.fx.glyphs.materialdesignicons.*?>
<?import de.jensd.fx.glyphs.octicons.*?>
<?import de.jensd.fx.glyphs.weathericons.*?>
<?import jfxtras.scene.control.*?>
<?import jfxtras.scene.control.agenda.*?>
<?import org.controlsfx.glyphfont.*?>
<?import impl.org.controlsfx.autocompletion.*?>
<?import javafx.collections.FXCollections?>
<?import javafx.scene.control.cell.PropertyValueFactory?>
<?import javafx.scene.control.cell.*?>
<?import javafx.collections.*?>
<?import Pharmacist.*?>

<fx:root id="background" prefHeight="612.0" prefWidth="868.0" type="javafx.scene.layout.AnchorPane" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
    <children>
        <TabPane layoutX="213.0" layoutY="60.0" prefHeight="200.0" prefWidth="200.0" side="LEFT" stylesheets="@../styles/tabbedPane.css" tabClosingPolicy="UNAVAILABLE" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="40.0">
        <tabs>
          <Tab text="Dashboard">   
            <graphic>
                
                    <FontAwesomeIconView fx:id="tab_ico0" glyphName="TACHOMETER" glyphStyle="" size="50px" textAlignment="LEFT" />
                  
            </graphic> 
            <content>
              <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                     <children>
                        <GridPane layoutX="14.0" layoutY="14.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="560.0" prefWidth="610.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                          <columnConstraints>
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="600.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="30.0" minWidth="30.0" prefWidth="30.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="600.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                          </columnConstraints>
                          <rowConstraints>
                            <RowConstraints maxHeight="6.0" minHeight="6.0" prefHeight="6.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="60.0" minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="250.0" minHeight="50.0" prefHeight="250.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="1.7976931348623157E308" minHeight="10.0" prefHeight="308.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="20.0" minHeight="15.0" prefHeight="20.0" vgrow="SOMETIMES" />
                          </rowConstraints>
                           <children>
                              <Label id="box" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="3" GridPane.rowIndex="5" />
                              <Label id="box" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="5" />
                              <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text=" Dashboard" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                <graphic>
                                    
                                    <Glyph fontFamily="FontAwesome" fontSize="30.0" icon="ARROW_CIRCLE_O_RIGHT" textFill="gray" />
                                      
                                </graphic>
                              </Label>
                              <PieChart id="piechart" fx:id="piechart" stylesheets="@../styles/chart.css" title="Test Reports (Last 30 Days)" GridPane.columnIndex="3" GridPane.rowIndex="5">
                                 <GridPane.margin>
                                    <Insets />
                                 </GridPane.margin></PieChart>
                              <AreaChart fx:id="labAppointments" title="Lab Appointments" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                <xAxis>
                                  <CategoryAxis side="BOTTOM" />
                                </xAxis>
                                <yAxis>
                                  <NumberAxis side="LEFT" />
                                </yAxis>
                              </AreaChart>
                              <GridPane GridPane.columnIndex="1" GridPane.columnSpan="3" GridPane.rowIndex="2" GridPane.rowSpan="2">
                                <columnConstraints>
                                  <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                                    <ColumnConstraints hgrow="SOMETIMES" maxWidth="30.0" minWidth="30.0" prefWidth="30.0" />
                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="250.0" minWidth="250.0" prefWidth="250.0" />
                                </columnConstraints>
                                <rowConstraints>
                                  <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                </rowConstraints>
                                 <children>
                                    <Label id="box" alignment="TOP_LEFT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Lab Appointments">
                                       <font>
                                          <Font name="System Bold" size="13.0" />
                                       </font>
                                    </Label>
                                    <HBox prefHeight="100.0" prefWidth="200.0">
                                       <GridPane.margin>
                                          <Insets bottom="10.0" left="10.0" right="10.0" top="40.0" />
                                       </GridPane.margin>
                                       <children>  
                                           <Agenda id="id1" fx:id="labAppointmentTable" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" />
                                       </children>  
                                    </HBox>
                                    <Label id="box" alignment="TOP_LEFT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Today" GridPane.columnIndex="2">
                                       <font>
                                          <Font name="System Bold" size="13.0" />
                                       </font>
                                    </Label>
                                    <Label fx:id="todayAppointments" alignment="CENTER" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="5" GridPane.columnIndex="2">
                                       <GridPane.margin>
                                          <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                                       </GridPane.margin>
                                       <font>
                                          <Font size="96.0" />
                                       </font>
                                    </Label>
                                 </children>
                              </GridPane>
                           </children>
                        </GridPane>
                     </children>
              </AnchorPane>   
            </content>
          </Tab>
          <Tab text="Lab Reports">
            <graphic>
                
                   <FontAwesomeIconView fx:id="tab_ico1" glyphName="FILE_ARCHIVE_ALT" glyphStyle="" size="40px" textAlignment="LEFT" />
                
            </graphic>  
            <content>
              <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                     <children>
                        <GridPane layoutX="62.0" layoutY="70.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="-0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                          <columnConstraints>
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="400.0" minWidth="10.0" prefWidth="100.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="30.0" minWidth="30.0" prefWidth="30.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="100.0" />
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                          </columnConstraints>
                          <rowConstraints>
                              <RowConstraints maxHeight="8.0" minHeight="8.0" prefHeight="8.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="60.0" minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="60.0" minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="30.0" minHeight="30.0" prefHeight="30.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="1.7976931348623157E308" minHeight="40.0" prefHeight="150.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                          </rowConstraints>
                           <children>
                              <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Lab Reports" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                <graphic>
                                    
                                    <Glyph fontFamily="FontAwesome" fontSize="30.0" icon="ARROW_CIRCLE_O_RIGHT" textFill="gray" />
                                      
                                </graphic>
                              </Label>
                              <Label id="box" alignment="TOP_LEFT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Patient" GridPane.columnIndex="1" GridPane.rowIndex="2" GridPane.rowSpan="7">
                                 <font>
                                    <Font name="System Bold" size="13.0" />
                                 </font>
                              </Label>
                              <Label alignment="TOP_RIGHT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" GridPane.columnIndex="3" GridPane.rowIndex="2">
                                 <graphic>
                                    
                                    <Glyph fontFamily="FontAwesome" fontSize="20.0" icon="INFO_CIRCLE" textFill="#222" />
                                      
                                </graphic>
                                  <padding>
                                    <Insets right="8.0" top="4.0" />
                                 </padding>
                              </Label>
                              <Label alignment="TOP_RIGHT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" GridPane.columnIndex="3" GridPane.rowIndex="2">
                                 <padding>
                                    <Insets right="5.0" top="5.0" />
                                 </padding>
                              </Label>
                              <ComboBox id="inputLabel01" fx:id="serachType" maxHeight="30.0" maxWidth="160.0" onAction="#serachTypeChanged" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                 <GridPane.margin>
                                    <Insets left="10.0" />
                                 </GridPane.margin>
                                 <items>
                                   <FXCollections fx:factory="observableArrayList">
                                       <String fx:value="Appointment ID" />
                                       <String fx:value="Report ID" />
                                    </FXCollections>
                                </items>
                              </ComboBox>
                              <Label id="inputLabel01" maxHeight="30.0" maxWidth="160.0" stylesheets="@../styles/box.css" text=" Name" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                 <GridPane.margin>
                                    <Insets left="10.0" />
                                 </GridPane.margin>
                              </Label>
                              <Label id="inputLabel01" maxHeight="30.0" maxWidth="160.0" stylesheets="@../styles/box.css" text=" Gender" GridPane.columnIndex="1" GridPane.rowIndex="6">
                                 <GridPane.margin>
                                    <Insets left="10.0" />
                                 </GridPane.margin>
                              </Label>
                              <Label id="inputLabel01" maxHeight="30.0" maxWidth="160.0" stylesheets="@../styles/box.css" text=" Age" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                 <GridPane.margin>
                                    <Insets left="10.0" />
                                 </GridPane.margin>
                              </Label>
                              <Label id="inputLabel01" maxHeight="30.0" maxWidth="160.0" stylesheets="@../styles/box.css" text=" Date" GridPane.columnIndex="1" GridPane.rowIndex="7">
                                 <GridPane.margin>
                                    <Insets left="10.0" />
                                 </GridPane.margin>
                              </Label>
                              <TextField id="inputText3" fx:id="appointmentIDtext" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                 <GridPane.margin>
                                    <Insets left="170.0" right="10.0" />
                                 </GridPane.margin>
                              </TextField>
                              <TextField id="inputText3" fx:id="patientNametext" editable="false" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                 <GridPane.margin>
                                    <Insets left="170.0" right="10.0" />
                                 </GridPane.margin>
                              </TextField>
                              <TextField id="inputText3" fx:id="patientGendertext" editable="false" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="6">
                                 <GridPane.margin>
                                    <Insets left="170.0" right="10.0" />
                                 </GridPane.margin>
                              </TextField>
                              <TextField id="inputText3" fx:id="patientAgetext" editable="false" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                 <GridPane.margin>
                                    <Insets left="170.0" right="10.0" />
                                 </GridPane.margin>
                              </TextField>
                              <TextField id="inputText3" fx:id="precrptionDate" editable="false" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="7">
                                 <GridPane.margin>
                                    <Insets left="170.0" right="10.0" />
                                 </GridPane.margin>
                              </TextField>
                              <GridPane GridPane.columnIndex="1" GridPane.rowIndex="8">
                                <columnConstraints>
                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="90.0" />
                                </columnConstraints>
                                <rowConstraints>
                                  <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                </rowConstraints>
                                 <children>
                                    <Button id="dark-blue" maxHeight="40.0" maxWidth="1.7976931348623157E308" mnemonicParsing="false" onAction="#searchAppointment" stylesheets="@../styles/buttons.css" text="Search">
                                       <GridPane.margin>
                                          <Insets left="10.0" right="10.0" />
                                       </GridPane.margin></Button>
                                 </children>
                              </GridPane>
                              <GridPane GridPane.columnIndex="3" GridPane.rowIndex="2" GridPane.rowSpan="9">
                                <columnConstraints>
                                  <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                                </columnConstraints>
                                <rowConstraints>
                                  <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                </rowConstraints>
                                 <children>
                                    <TabPane fx:id="reportTabs" prefHeight="200.0" prefWidth="200.0" side="LEFT" stylesheets="@../styles/tabbedPane3.css" tabClosingPolicy="UNAVAILABLE">
                                       <tabs>
                                          <Tab fx:id="pt" disable="true" text="Pathalogy Test">
                                             <content>
                                                <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                                   <children>
                                                      <GridPane layoutX="56.0" layoutY="28.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                                         <children>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Appearance (g/dl)" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" S.G(Refractometer) (g/dl)" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Ph (g/dl)" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Protein (g/dl)" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Glucose (g/dl)" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Ketone Bodies (g/dl)" GridPane.columnIndex="1" GridPane.rowIndex="6">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Bilirubin (g/dl)" GridPane.columnIndex="1" GridPane.rowIndex="7">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Urobilinogen (g/dl)" GridPane.columnIndex="4" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Contrifuged Deposits (g/dl)" GridPane.columnIndex="4" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Pus Cells (g/dl)" GridPane.columnIndex="4" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Red Cells (g/dl)" GridPane.columnIndex="4" GridPane.rowIndex="4">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Epithelial Cells (g/dl)" GridPane.columnIndex="4" GridPane.rowIndex="5">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Cristals (g/dl)" GridPane.columnIndex="4" GridPane.rowIndex="6">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Casts (g/dl)" GridPane.columnIndex="4" GridPane.rowIndex="7">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <TextField id="inputText3" fx:id="appearancetxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="sgRefractometertxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="phtxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="proteintxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="4">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="glucosetxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="5">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="ketoneBodiestxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="6">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="bilirubintxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="7">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="urobilirubintxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="5" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="contrifugedDepositsphaseContrastMicroscopytxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="5" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="pusCellstxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="5" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="redCellstxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="5" GridPane.rowIndex="4">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="epithelialCellstxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="5" GridPane.rowIndex="5">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="cristalstxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="5" GridPane.rowIndex="6">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="caststxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="5" GridPane.rowIndex="7">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Pathalogy Test" GridPane.columnIndex="1" GridPane.columnSpan="3">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               <font>
                                                                  <Font size="15.0" />
                                                               </font>
                                                            </Label>
                                                            <GridPane GridPane.columnIndex="1" GridPane.columnSpan="5" GridPane.rowIndex="8">
                                                              <columnConstraints>
                                                                <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="5.0" prefWidth="5.0" />
                                                                <ColumnConstraints hgrow="SOMETIMES" maxWidth="120.0" minWidth="120.0" prefWidth="120.0" />
                                                              </columnConstraints>
                                                              <rowConstraints>
                                                                <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                              </rowConstraints>
                                                               <children>
                                                                  <Button id="dark-blue" maxHeight="30.0" maxWidth="40.0" mnemonicParsing="false" onAction="#clearPathalogyTest" stylesheets="@../styles/buttons.css" GridPane.columnIndex="1">
                                                                    <graphic>   
                                                                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="ERASER" textFill="#eee" />
                                                                    </graphic>
                                                                  </Button>    
                                                                  <Button id="dark-blue" maxHeight="30.0" maxWidth="60.0" mnemonicParsing="false" onAction="#savePathalogyTest" stylesheets="@../styles/buttons.css" text="Save" GridPane.columnIndex="1">
                                                                     <GridPane.margin>
                                                                        <Insets left="50.0" />
                                                                     </GridPane.margin>
                                                                  </Button>
                                                               </children>
                                                            </GridPane>
                                                         </children>
                                                         <columnConstraints>
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="30.0" minWidth="5.0" prefWidth="30.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="150.0" minWidth="60.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="20.0" minWidth="20.0" prefWidth="20.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="150.0" minWidth="10.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="30.0" minWidth="5.0" prefWidth="30.0" />
                                                         </columnConstraints>
                                                         <rowConstraints>
                                                            <RowConstraints maxHeight="60.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="60.0" minHeight="30.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                         </rowConstraints>
                                                      </GridPane>
                                                   </children>
                                                </AnchorPane>
                                             </content>
                                          </Tab>
                                          <Tab fx:id="lpt" disable="true" text="Lipid Profile Test">
                                             <content>
                                                <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                                   <children>
                                                      <GridPane AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                                         <children>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Cholestrol Test" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Cholestrol HDL" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Triglycerides" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Total Cholestrol LDL/ HDL ratio" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <TextField id="inputLabel11" fx:id="cholestrolHDLtxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputLabel11" fx:id="cholestrolLDLtxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputLabel11" fx:id="triglyceridestxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputLabel11" fx:id="totalCholestrolLDLHDLratiotxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="4">
                                                               <GridPane.margin>
                                                                  <Insets />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Lipid Profile Test" GridPane.columnIndex="1" GridPane.columnSpan="3">
                                                               <font>
                                                                  <Font size="15.0" />
                                                               </font>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <GridPane GridPane.columnIndex="1" GridPane.columnSpan="3" GridPane.rowIndex="5">
                                                               <children>
                                                                  <Button id="dark-blue" maxHeight="30.0" maxWidth="40.0" mnemonicParsing="false" onAction="#clearLipidTest" stylesheets="@../styles/buttons.css" GridPane.columnIndex="1">
                                                                    <graphic>   
                                                                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="ERASER" textFill="#eee" />
                                                                    </graphic>
                                                                  </Button> 
                                                                  <Button id="dark-blue" maxHeight="30.0" maxWidth="60.0" mnemonicParsing="false" onAction="#saveLipidTest" stylesheets="@../styles/buttons.css" text="Save" GridPane.columnIndex="1">
                                                                     <GridPane.margin>
                                                                        <Insets left="50.0" />
                                                                     </GridPane.margin>
                                                                  </Button>
                                                               </children>
                                                               <columnConstraints>
                                                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="5.0" prefWidth="5.0" />
                                                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="120.0" minWidth="120.0" prefWidth="120.0" />
                                                               </columnConstraints>
                                                               <rowConstraints>
                                                                  <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                               </rowConstraints>
                                                            </GridPane>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" 35-45 mg /dl" GridPane.columnIndex="3" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" 200-239 mg/dl" GridPane.columnIndex="3" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" 150-199 mg/dl" GridPane.columnIndex="3" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" 5.0" GridPane.columnIndex="3" GridPane.rowIndex="4">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                         </children>
                                                         <columnConstraints>
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="40.0" minWidth="5.0" prefWidth="30.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="200.0" minWidth="60.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="120.0" minWidth="10.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="40.0" minWidth="5.0" prefWidth="30.0" />
                                                         </columnConstraints>
                                                         <rowConstraints>
                                                            <RowConstraints maxHeight="60.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="60.0" minHeight="30.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                         </rowConstraints>
                                                      </GridPane>
                                                   </children></AnchorPane>
                                             </content>
                                          </Tab>
                                          <Tab fx:id="bg" disable="true" text="Blood Grouping &amp; Rh">
                                             <content>
                                                <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                                   <children>
                                                      <GridPane AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                                         <children>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Blood Group" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Rhesus D" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <TextField id="inputText3" fx:id="bloodGtxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="rhDtxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Blood Grouping &amp; Rh" GridPane.columnIndex="1" GridPane.columnSpan="3">
                                                               <font>
                                                                  <Font size="15.0" />
                                                               </font>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <GridPane GridPane.columnIndex="1" GridPane.columnSpan="2" GridPane.rowIndex="3">
                                                               <children>
                                                                  <Button id="dark-blue" maxHeight="30.0" maxWidth="40.0" mnemonicParsing="false" onAction="#clearBloodGroupTest" stylesheets="@../styles/buttons.css" GridPane.columnIndex="1">
                                                                    <graphic>   
                                                                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="ERASER" textFill="#eee" />
                                                                    </graphic>
                                                                  </Button> 
                                                                  <Button id="dark-blue" maxHeight="30.0" maxWidth="60.0" mnemonicParsing="false" onAction="#saveBloodGroupTest" stylesheets="@../styles/buttons.css" text="Save" GridPane.columnIndex="1">
                                                                     <GridPane.margin>
                                                                        <Insets left="50.0" />
                                                                     </GridPane.margin>
                                                                  </Button>
                                                               </children>
                                                               <columnConstraints>
                                                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="5.0" prefWidth="5.0" />
                                                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="120.0" minWidth="120.0" prefWidth="120.0" />
                                                               </columnConstraints>
                                                               <rowConstraints>
                                                                  <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                               </rowConstraints>
                                                            </GridPane>
                                                         </children>
                                                         <columnConstraints>
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="40.0" minWidth="5.0" prefWidth="30.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="200.0" minWidth="60.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="40.0" minWidth="5.0" prefWidth="30.0" />
                                                         </columnConstraints>
                                                         <rowConstraints>
                                                            <RowConstraints maxHeight="60.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="60.0" minHeight="30.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                         </rowConstraints>
                                                      </GridPane>
                                                   </children></AnchorPane>
                                             </content>
                                          </Tab>
                                          <Tab fx:id="cbc" disable="true" text="Complete Blood Count">
                                             <content>
                                                <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                                   <children>
                                                      <GridPane AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                                         <children>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Neutrophils (g/dl)" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Lymphocytes (g/dl)" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Eosinophils (g/dl)" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Monocytes (g/dl)" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Basophils (g/dl)" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Mean Cell Haemoglobin (g/dl)" GridPane.columnIndex="1" GridPane.rowIndex="6">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Mean Cell Volume (g/dl)" GridPane.columnIndex="1" GridPane.rowIndex="7">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Differential Count (g/dl)" GridPane.columnIndex="4" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Total White Cell Count (g/dl)" GridPane.columnIndex="4" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Haemoglobin (g/dl)" GridPane.columnIndex="4" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Red blood Cells (g/dl)" GridPane.columnIndex="4" GridPane.rowIndex="4">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" M.C.H Concentration (g/dl)" GridPane.columnIndex="4" GridPane.rowIndex="5">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Haematocrit (g/dl)" GridPane.columnIndex="4" GridPane.rowIndex="6">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Red Cells Distribution Width (g/dl)" GridPane.columnIndex="4" GridPane.rowIndex="7">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <TextField id="inputText3" fx:id="neutrophilstxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="lymphocytestxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="eosonophilstxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="monocytestxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="4">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="basophilstxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="5">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="meanCellHaemoglobintxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="6">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="meanCellVolumetxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="7">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="differentialCounttxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="5" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="totalWhiteCellCounttxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="5" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="haemoglobintxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="5" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="redBloodCellstxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="5" GridPane.rowIndex="4">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="mchConcentrationtxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="5" GridPane.rowIndex="5">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="haematocrittxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="5" GridPane.rowIndex="6">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputText3" fx:id="redCellsDistributionWidthtxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="5" GridPane.rowIndex="7">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Complete Blood Count" GridPane.columnIndex="1" GridPane.columnSpan="3">
                                                               <font>
                                                                  <Font size="15.0" />
                                                               </font>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <GridPane GridPane.columnIndex="1" GridPane.columnSpan="5" GridPane.rowIndex="9">
                                                               <children>
                                                                  <Button id="dark-blue" maxHeight="30.0" maxWidth="40.0" mnemonicParsing="false" onAction="#clearCompleteBlood" stylesheets="@../styles/buttons.css" GridPane.columnIndex="1">
                                                                    <graphic>   
                                                                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="ERASER" textFill="#eee" />
                                                                    </graphic>
                                                                  </Button> 
                                                                  <Button id="dark-blue" maxHeight="30.0" maxWidth="60.0" mnemonicParsing="false" onAction="#saveCompleteBlood" stylesheets="@../styles/buttons.css" text="Save" GridPane.columnIndex="1">
                                                                     <GridPane.margin>
                                                                        <Insets left="50.0" />
                                                                     </GridPane.margin>
                                                                  </Button>
                                                               </children>
                                                               <columnConstraints>
                                                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="5.0" prefWidth="5.0" />
                                                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="120.0" minWidth="120.0" prefWidth="120.0" />
                                                               </columnConstraints>
                                                               <rowConstraints>
                                                                  <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                               </rowConstraints>
                                                            </GridPane>
                                                            <TextField id="inputText3" fx:id="plateletCounttxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="8">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Platelet Count (g/dl)" GridPane.columnIndex="1" GridPane.rowIndex="8">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                         </children>
                                                         <columnConstraints>
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="30.0" minWidth="5.0" prefWidth="30.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="150.0" minWidth="60.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="20.0" minWidth="20.0" prefWidth="20.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="150.0" minWidth="10.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="30.0" minWidth="5.0" prefWidth="30.0" />
                                                         </columnConstraints>
                                                         <rowConstraints>
                                                            <RowConstraints maxHeight="60.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="60.0" minHeight="30.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                         </rowConstraints>
                                                      </GridPane>
                                                   </children></AnchorPane>
                                             </content>
                                          </Tab>
                                          <Tab fx:id="lft" disable="true" text="LFT">
                                             <content>
                                                <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                                   <children>
                                                      <GridPane AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                                         <children>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Total Protein" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Albumin" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Globulin" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Total Bilirubin" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Direct Bilirubin" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <TextField id="inputLabel11" fx:id="totalProteintxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputLabel11" fx:id="albumintxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputLabel11" fx:id="globulintxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputLabel11" fx:id="totalBilirubintxt2" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="4">
                                                               <GridPane.margin>
                                                                  <Insets />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputLabel11" fx:id="directBilirubintxt2" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="5">
                                                               <GridPane.margin>
                                                                  <Insets />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Liver Function Test" GridPane.columnIndex="1" GridPane.columnSpan="3">
                                                               <font>
                                                                  <Font size="15.0" />
                                                               </font>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <GridPane GridPane.columnIndex="1" GridPane.columnSpan="3" GridPane.rowIndex="9">
                                                               <children>
                                                                  <Button id="dark-blue" maxHeight="30.0" maxWidth="40.0" mnemonicParsing="false" onAction="#clearLiverFunctionTest" stylesheets="@../styles/buttons.css" GridPane.columnIndex="1">
                                                                    <graphic>   
                                                                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="ERASER" textFill="#eee" />
                                                                    </graphic>
                                                                  </Button> 
                                                                  <Button id="dark-blue" maxHeight="30.0" maxWidth="60.0" mnemonicParsing="false" onAction="#saveLiverFunctionTest" stylesheets="@../styles/buttons.css" text="Save" GridPane.columnIndex="1">
                                                                     <GridPane.margin>
                                                                        <Insets left="50.0" />
                                                                     </GridPane.margin>
                                                                  </Button>
                                                               </children>
                                                               <columnConstraints>
                                                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="5.0" prefWidth="5.0" />
                                                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="120.0" minWidth="120.0" prefWidth="120.0" />
                                                               </columnConstraints>
                                                               <rowConstraints>
                                                                  <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                               </rowConstraints>
                                                            </GridPane>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" &lt;0.3 mg/dL" GridPane.columnIndex="3" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" 6.6-8.7 g/dL" GridPane.columnIndex="3" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" 1.5-3.0 g/dL" GridPane.columnIndex="3" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" 0.00-1.20 mg/dL" GridPane.columnIndex="3" GridPane.rowIndex="4">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" 3.5 -5.2 g/dL" GridPane.columnIndex="3" GridPane.rowIndex="5">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" M 0-40, F 0-32 U/L" GridPane.columnIndex="3" GridPane.rowIndex="6">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <TextField id="inputLabel11" fx:id="sgotasttxt2" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="6" />
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" SGOT(AST)" GridPane.columnIndex="1" GridPane.rowIndex="6">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" M 0-41, F 0-33 U/L" GridPane.columnIndex="3" GridPane.rowIndex="7">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <TextField id="inputLabel11" fx:id="sgptalttxt2" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="7" />
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" SGPT(ALT)" GridPane.columnIndex="1" GridPane.rowIndex="7">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" M 40-130, F 35-105 U/L" GridPane.columnIndex="3" GridPane.rowIndex="8">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <TextField id="inputLabel11" fx:id="alkalinePhospatestxt2" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="8" />
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Alkene Phospates" GridPane.columnIndex="1" GridPane.rowIndex="8">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                         </children>
                                                         <columnConstraints>
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="40.0" minWidth="5.0" prefWidth="30.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="140.0" minWidth="60.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="180.0" minWidth="10.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="40.0" minWidth="5.0" prefWidth="30.0" />
                                                         </columnConstraints>
                                                         <rowConstraints>
                                                            <RowConstraints maxHeight="60.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="40.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="40.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="40.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="40.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="40.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="40.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="40.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="40.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="60.0" minHeight="30.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                         </rowConstraints>
                                                      </GridPane>
                                                   </children></AnchorPane>
                                             </content>
                                          </Tab>
                                          <Tab fx:id="rft" disable="true" text="RFT">
                                             <content>
                                                <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                                   <children>
                                                      <GridPane AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                                         <children>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Creatine" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Urea" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Total Bilirubin" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Direct Bilirubin" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" SGOT(AST)" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <TextField id="inputLabel11" fx:id="creatininetxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputLabel11" fx:id="ureatxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputLabel11" fx:id="totalBilirubintxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputLabel11" fx:id="directBilirubintxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="4">
                                                               <GridPane.margin>
                                                                  <Insets />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputLabel11" fx:id="sgotasttxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="5">
                                                               <GridPane.margin>
                                                                  <Insets />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Renal Function Test" GridPane.columnIndex="1" GridPane.columnSpan="3">
                                                               <font>
                                                                  <Font size="15.0" />
                                                               </font>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <GridPane GridPane.columnIndex="1" GridPane.columnSpan="3" GridPane.rowIndex="8">
                                                               <children>
                                                                  <Button id="dark-blue" maxHeight="30.0" maxWidth="40.0" mnemonicParsing="false" onAction="#clearRenalTest" stylesheets="@../styles/buttons.css" GridPane.columnIndex="1">
                                                                    <graphic>   
                                                                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="ERASER" textFill="#eee" />
                                                                    </graphic>
                                                                  </Button> 
                                                                  <Button id="dark-blue" maxHeight="30.0" maxWidth="60.0" mnemonicParsing="false" onAction="#saveRenalTest" stylesheets="@../styles/buttons.css" text="Save" GridPane.columnIndex="1">
                                                                     <GridPane.margin>
                                                                        <Insets left="50.0" />
                                                                     </GridPane.margin>
                                                                  </Button>
                                                               </children>
                                                               <columnConstraints>
                                                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="5.0" prefWidth="5.0" />
                                                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="120.0" minWidth="120.0" prefWidth="120.0" />
                                                               </columnConstraints>
                                                               <rowConstraints>
                                                                  <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                               </rowConstraints>
                                                            </GridPane>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" 08-1.2 g/dL" GridPane.columnIndex="3" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" 18-40 g/dL" GridPane.columnIndex="3" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" 0.4 g/dL" GridPane.columnIndex="3" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" 0.2 g/dL" GridPane.columnIndex="3" GridPane.rowIndex="4">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" 8 g/dL" GridPane.columnIndex="3" GridPane.rowIndex="5">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" 10 g/dL" GridPane.columnIndex="3" GridPane.rowIndex="6">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" SGOT(ALT)" GridPane.columnIndex="1" GridPane.rowIndex="6">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <TextField id="inputLabel11" fx:id="sgptalttxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="6" />
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Alkaline Phosphatase Level" GridPane.columnIndex="1" GridPane.rowIndex="7">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" 10 g/dL" GridPane.columnIndex="3" GridPane.rowIndex="7">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <TextField id="inputLabel11" fx:id="alkalinePhospatestxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="7" />
                                                         </children>
                                                         <columnConstraints>
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="40.0" minWidth="5.0" prefWidth="30.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="200.0" minWidth="60.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="120.0" minWidth="10.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="40.0" minWidth="5.0" prefWidth="30.0" />
                                                         </columnConstraints>
                                                         <rowConstraints>
                                                            <RowConstraints maxHeight="60.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="60.0" minHeight="30.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                            <RowConstraints minHeight="5.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                         </rowConstraints>
                                                      </GridPane>
                                                   </children></AnchorPane>
                                             </content>
                                          </Tab>
                                          <Tab fx:id="cpk" disable="true" text="CPK">
                                             <content>
                                                <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                                   <children>
                                                      <GridPane AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                                         <children>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" CPK Total" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <TextField id="inputLabel11" fx:id="cpkTotaltxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Creatine Phosphokinase Test " GridPane.columnIndex="1" GridPane.columnSpan="3">
                                                               <font>
                                                                  <Font size="15.0" />
                                                               </font>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <GridPane GridPane.columnIndex="1" GridPane.columnSpan="3" GridPane.rowIndex="2">
                                                               <children>
                                                                  <Button id="dark-blue" maxHeight="30.0" maxWidth="40.0" mnemonicParsing="false" onAction="#clearCreatineTest" stylesheets="@../styles/buttons.css" GridPane.columnIndex="1">
                                                                    <graphic>   
                                                                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="ERASER" textFill="#eee" />
                                                                    </graphic>
                                                                  </Button> 
                                                                  <Button id="dark-blue" maxHeight="30.0" maxWidth="60.0" mnemonicParsing="false" onAction="#saveCreatineTest" stylesheets="@../styles/buttons.css" text="Save" GridPane.columnIndex="1">
                                                                     <GridPane.margin>
                                                                        <Insets left="50.0" />
                                                                     </GridPane.margin>
                                                                  </Button>
                                                               </children>
                                                               <columnConstraints>
                                                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="5.0" prefWidth="5.0" />
                                                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="120.0" minWidth="120.0" prefWidth="120.0" />
                                                               </columnConstraints>
                                                               <rowConstraints>
                                                                  <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                               </rowConstraints>
                                                            </GridPane>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" 26-192 U/L" GridPane.columnIndex="3" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                         </children>
                                                         <columnConstraints>
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="40.0" minWidth="5.0" prefWidth="30.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="200.0" minWidth="60.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="120.0" minWidth="10.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="40.0" minWidth="5.0" prefWidth="30.0" />
                                                         </columnConstraints>
                                                         <rowConstraints>
                                                            <RowConstraints maxHeight="60.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="60.0" minHeight="30.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                         </rowConstraints>
                                                      </GridPane>
                                                   </children></AnchorPane>
                                             </content>
                                          </Tab>
                                          <Tab fx:id="hiv" disable="true" text="HIV">
                                             <content>
                                                <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                                   <children>
                                                      <GridPane AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                                         <children>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" HIV 1 &amp; 2 ELISA" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputLabel01" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <TextField id="inputLabel11" fx:id="hiv12ELISAtxt" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputLabel11" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <TextField id="inputLabel11" maxHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets />
                                                               </GridPane.margin>
                                                            </TextField>
                                                            <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Lipid Profile Test" GridPane.columnIndex="1" GridPane.columnSpan="3">
                                                               <font>
                                                                  <Font size="15.0" />
                                                               </font>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <GridPane GridPane.columnIndex="1" GridPane.columnSpan="3" GridPane.rowIndex="4">
                                                               <children>
                                                                  <Button id="dark-blue" maxHeight="30.0" maxWidth="40.0" mnemonicParsing="false" onAction="#clearHIV" stylesheets="@../styles/buttons.css" GridPane.columnIndex="1">
                                                                    <graphic>   
                                                                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="ERASER" textFill="#eee" />
                                                                    </graphic>
                                                                  </Button> 
                                                                  <Button id="dark-blue" maxHeight="30.0" maxWidth="60.0" mnemonicParsing="false" onAction="#saveHIV" stylesheets="@../styles/buttons.css" text="Save" GridPane.columnIndex="1">
                                                                     <GridPane.margin>
                                                                        <Insets left="50.0" />
                                                                     </GridPane.margin>
                                                                  </Button>
                                                               </children>
                                                               <columnConstraints>
                                                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="5.0" prefWidth="5.0" />
                                                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="120.0" minWidth="120.0" prefWidth="120.0" />
                                                               </columnConstraints>
                                                               <rowConstraints>
                                                                  <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                               </rowConstraints>
                                                            </GridPane>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Negative: &lt;=0.90" GridPane.columnIndex="3" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Equivocal:  0.91 - 1.10" GridPane.columnIndex="3" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                            <Label id="inputText2" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Positive: &gt;= 1.11" GridPane.columnIndex="3" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets right="10.0" />
                                                               </GridPane.margin>
                                                            </Label>
                                                         </children>
                                                         <columnConstraints>
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="40.0" minWidth="5.0" prefWidth="30.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="200.0" minWidth="60.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="200.0" minWidth="10.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="40.0" minWidth="5.0" prefWidth="30.0" />
                                                         </columnConstraints>
                                                         <rowConstraints>
                                                            <RowConstraints maxHeight="60.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="60.0" minHeight="30.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                         </rowConstraints>
                                                      </GridPane>
                                                   </children></AnchorPane>
                                             </content>
                                          </Tab>
                                       </tabs>
                                    </TabPane>
                                 </children>
                              </GridPane>
                              <Label id="box" alignment="TOP_LEFT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Additional" GridPane.columnIndex="1" GridPane.rowIndex="10">
                                 <font>
                                    <Font name="System Bold" size="13.0" />
                                 </font></Label>
                              <GridPane GridPane.columnIndex="1" GridPane.rowIndex="10">
                                <columnConstraints>
                                  <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="70.0" minWidth="10.0" prefWidth="70.0" />
                                </columnConstraints>
                                <rowConstraints>
                                  <RowConstraints maxHeight="30.0" minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                  <RowConstraints maxHeight="1.7976931348623157E308" minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                                </rowConstraints>
                                 <children>
                                    <TextArea id="textBorder" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/box.css" GridPane.columnSpan="2" GridPane.rowIndex="1">
                                       <GridPane.margin>
                                          <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                                       </GridPane.margin>
                                    </TextArea>
                                 </children>
                              </GridPane>
                           </children>
                        </GridPane>
                     </children></AnchorPane>
            </content>
          </Tab>
          <Tab text="Profile">
            <graphic>
                
                    <FontAwesomeIconView fx:id="tab_ico2" glyphName="USER" glyphStyle="" size="50px" textAlignment="LEFT" />
                    
            </graphic>  
            <content>
              <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                     <children>
                        <GridPane layoutX="83.0" layoutY="77.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                          <columnConstraints>
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="200.0" minWidth="200.0" prefWidth="200.0" />
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="50.0" prefWidth="600.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="41.0" prefWidth="41.0" />
                          </columnConstraints>
                          <rowConstraints>
                            <RowConstraints maxHeight="8.0" minHeight="8.0" prefHeight="8.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="80.0" minHeight="80.0" prefHeight="80.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="100000.0" minHeight="200.0" prefHeight="200.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                          </rowConstraints>
                           <children>
                              <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Profile" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                <graphic>  
                                     
                                    <Glyph fontFamily="FontAwesome" fontSize="30.0" icon="ARROW_CIRCLE_O_RIGHT" textFill="gray" />
                                    
                                </graphic>
                              </Label>
                              <TabPane maxHeight="1000.0" maxWidth="1000.0" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/tabbedPane3.css" tabClosingPolicy="UNAVAILABLE" GridPane.columnIndex="2" GridPane.rowIndex="2">
                                <tabs>
                                  <Tab text="Basic Details">
                                    <content>
                                      <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                             <children>
                                                <GridPane layoutX="66.0" layoutY="69.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                                  <columnConstraints>
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                                                    <ColumnConstraints hgrow="SOMETIMES" maxWidth="450.0" minWidth="10.0" prefWidth="228.0" />
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="50.0" prefWidth="100.0" />
                                                  </columnConstraints>
                                                  <rowConstraints>
                                                      <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                    <RowConstraints maxHeight="220.0" minHeight="220.0" prefHeight="220.0" vgrow="SOMETIMES" />
                                                    <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                    <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="1.7976931348623157E308" minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                                                  </rowConstraints>
                                                   <children>
                                                      <GridPane prefHeight="212.0" prefWidth="279.0" GridPane.columnIndex="1" GridPane.rowIndex="1" GridPane.rowSpan="7">
                                                        <columnConstraints>
                                                          <ColumnConstraints hgrow="SOMETIMES" maxWidth="206.0" minWidth="10.0" prefWidth="149.0" />
                                                          <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="136.0" />
                                                        </columnConstraints>
                                                        <rowConstraints>
                                                            <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                                                          <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                          <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                          <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="1.7976931348623157E308" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                        </rowConstraints>
                                                         <children>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Name" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" prefHeight="16.0" prefWidth="86.0" stylesheets="@../styles/box.css" text="Date Of Birth" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="NIC" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Age" GridPane.rowIndex="4">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Gender" GridPane.rowIndex="5">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Nationality" GridPane.rowIndex="6">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Mobile" GridPane.rowIndex="8">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Email" GridPane.rowIndex="9">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Address" GridPane.rowIndex="10">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Religion" GridPane.rowIndex="7">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <TextField id="inputText1" fx:id="labName" disable="true" maxHeight="30.0" maxWidth="650.0" minHeight="30.0" prefWidth="500.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                                                            <TextField id="inputText1" fx:id="labNIC" disable="true" maxHeight="30.0" maxWidth="650.0" minHeight="30.0" prefWidth="500.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                                                            <TextField id="inputText1" fx:id="labAge" disable="true" maxHeight="30.0" maxWidth="650.0" minHeight="30.0" prefWidth="500.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="4" />
                                                            <TextField id="inputText1" fx:id="labNationality" disable="true" maxHeight="30.0" maxWidth="650.0" minHeight="30.0" prefWidth="500.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="6" />
                                                            <TextField id="inputText1" fx:id="labReligion" disable="true" maxHeight="30.0" maxWidth="650.0" minHeight="30.0" prefWidth="500.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="7" />
                                                            <TextField id="inputText1" fx:id="labMobile" disable="true" maxHeight="30.0" maxWidth="650.0" minHeight="30.0" prefWidth="500.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="8" />
                                                            <TextField id="inputText1" fx:id="labEmail" disable="true" maxHeight="30.0" maxWidth="650.0" minHeight="30.0" prefWidth="500.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="9" />
                                                            <TextField id="inputText1" fx:id="labAddress" disable="true" maxHeight="30.0" maxWidth="650.0" minHeight="30.0" prefWidth="500.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="10" />
                                                            <DatePicker id="inputText1" fx:id="labDOB" disable="true" maxHeight="30.0" maxWidth="650.0" minHeight="30.0" prefWidth="500.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                                                            <ComboBox id="inputText1" fx:id="labGender" disable="true" maxHeight="30.0" maxWidth="650.0" minWidth="30.0" prefWidth="500.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                                                <items>
                                                                   <FXCollections fx:factory="observableArrayList">
                                                                       <String fx:value="Male" />
                                                                       <String fx:value="Female" />
                                                                    </FXCollections>
                                                                </items>
                                                            </ComboBox>    
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="1">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="USER" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="2">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="CERTIFICATE" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="3">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="CALENDAR" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="4">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="STREET_VIEW" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="5">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="MALE" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="6">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="BELL" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="7">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                                <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="BULLSEYE" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="8">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="MOBILE_PHONE" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="9">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="ENVELOPE" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="10">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                                <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="MAP_MARKER" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Button id="dark-blue" fx:id="editBasicInfoButton" maxHeight="30.0" maxWidth="60.0" minHeight="30.0" minWidth="60.0" mnemonicParsing="false" onAction="#editBasicInfo" stylesheets="@../styles/buttons.css" text="Edit" GridPane.rowIndex="11">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Button>
                                                         </children>
                                                      </GridPane>
                                                   </children>
                                                </GridPane>
                                             </children></AnchorPane>
                                    </content>
                                  </Tab>
                                  <Tab text="Lab Assistant">
                                    <content>
                                      <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                             <children>
                                                <GridPane layoutX="159.0" layoutY="142.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                                  <columnConstraints>
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                                                    <ColumnConstraints hgrow="SOMETIMES" maxWidth="200.0" minWidth="200.0" prefWidth="200.0" />
                                                    <ColumnConstraints hgrow="SOMETIMES" maxWidth="335.0" minWidth="10.0" prefWidth="335.0" />
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="50.0" prefWidth="100.0" />
                                                  </columnConstraints>
                                                  <rowConstraints>
                                                    <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="90.0" minHeight="90.0" prefHeight="90.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="90.0" minHeight="90.0" prefHeight="90.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="90.0" minHeight="90.0" prefHeight="90.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="60.0" minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="60.0" minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
                                                    <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                  </rowConstraints>
                                                   <children>
                                                      <Label id="inputLabel1" maxHeight="80.0" maxWidth="160.0" minHeight="80.0" minWidth="160.0" stylesheets="@../styles/box.css" text="Education" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                         <opaqueInsets>
                                                            <Insets />
                                                         </opaqueInsets>
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="80.0" maxWidth="160.0" minHeight="80.0" minWidth="160.0" stylesheets="@../styles/box.css" text="Training" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                         <opaqueInsets>
                                                            <Insets />
                                                         </opaqueInsets>
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="80.0" maxWidth="160.0" minHeight="80.0" minWidth="160.0" stylesheets="@../styles/box.css" text="Experience" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                                         <opaqueInsets>
                                                            <Insets />
                                                         </opaqueInsets>
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="50.0" maxWidth="160.0" minHeight="50.0" minWidth="160.0" stylesheets="@../styles/box.css" text="Achivements" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                                         <opaqueInsets>
                                                            <Insets />
                                                         </opaqueInsets>
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="50.0" maxWidth="160.0" minHeight="50.0" minWidth="160.0" stylesheets="@../styles/box.css" text="Other" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                                         <opaqueInsets>
                                                            <Insets />
                                                         </opaqueInsets>
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <TextArea id="inputText1" fx:id="labEducation" disable="true" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/box.css" wrapText="true" GridPane.columnIndex="2" GridPane.rowIndex="1">
                                                         <GridPane.margin>
                                                            <Insets bottom="5.0" top="5.0" />
                                                         </GridPane.margin>
                                                      </TextArea>
                                                      <TextArea id="inputText1" fx:id="labTraining" disable="true" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/box.css" wrapText="true" GridPane.columnIndex="2" GridPane.rowIndex="2">
                                                         <GridPane.margin>
                                                            <Insets bottom="5.0" top="5.0" />
                                                         </GridPane.margin>
                                                      </TextArea>
                                                      <TextArea id="inputText1" fx:id="labExperience" disable="true" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/box.css" wrapText="true" GridPane.columnIndex="2" GridPane.rowIndex="3">
                                                         <GridPane.margin>
                                                            <Insets bottom="5.0" top="5.0" />
                                                         </GridPane.margin>
                                                      </TextArea>
                                                      <TextArea id="inputText1" fx:id="labAchivements" disable="true" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/box.css" wrapText="true" GridPane.columnIndex="2" GridPane.rowIndex="4">
                                                         <GridPane.margin>
                                                            <Insets bottom="5.0" top="5.0" />
                                                         </GridPane.margin>
                                                      </TextArea>
                                                      <TextArea id="inputText1" fx:id="labOther" disable="true" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/box.css" wrapText="true" GridPane.columnIndex="2" GridPane.rowIndex="5">
                                                         <GridPane.margin>
                                                            <Insets bottom="5.0" top="5.0" />
                                                         </GridPane.margin>
                                                      </TextArea>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="80.0" maxWidth="30.0" minHeight="80.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                          <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="LEANPUB" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="80.0" maxWidth="30.0" minHeight="80.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                          <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="HEARTBEAT" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="80.0" maxWidth="30.0" minHeight="80.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                          <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="STETHOSCOPE" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="50.0" maxWidth="30.0" minHeight="50.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                          <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="TROPHY" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="50.0" maxWidth="30.0" minHeight="50.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                          <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="STAR" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Button id="dark-blue" fx:id="editLabInfoButton" maxHeight="30.0" maxWidth="60.0" minHeight="30.0" minWidth="60.0" mnemonicParsing="false" onAction="#editLabInfo" stylesheets="@../styles/buttons.css" text="Edit" GridPane.columnIndex="1" GridPane.rowIndex="6">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                      </Button>
                                                   </children>
                                                </GridPane>
                                             </children></AnchorPane>
                                    </content>
                                  </Tab>
                                  <Tab text="Account">
                                    <content>
                                      <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                             <children>
                                                <GridPane layoutX="43.0" layoutY="72.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                                  <columnConstraints>
                                                    <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="170.0" minWidth="170.0" prefWidth="170.0" />
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="300.0" minWidth="10.0" prefWidth="300.0" />
                                                    <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="50.0" prefWidth="100.0" />
                                                  </columnConstraints>
                                                  <rowConstraints>
                                                    <RowConstraints maxHeight="60.0" minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
                                                    <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                    <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                  </rowConstraints>
                                                   <children>
                                                      <Label id="inputLabel1" maxHeight="30.0" maxWidth="130.0" minHeight="30.0" minWidth="130.0" stylesheets="@../styles/box.css" text="User Name" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="30.0" maxWidth="130.0" minHeight="30.0" minWidth="130.0" stylesheets="@../styles/box.css" text="User Type" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="30.0" maxWidth="130.0" minHeight="30.0" minWidth="130.0" stylesheets="@../styles/box.css" text="User ID" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <TextField id="inputText1" fx:id="labUserName" disable="true" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                                                      <TextField id="inputText1" fx:id="labUserType" disable="true" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="2" />
                                                      <TextField id="inputText1" fx:id="labUserID" disable="true" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="3" />
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                         <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="USER" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                         <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="USERS" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                         <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="BULLSEYE" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="8">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                         <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="KEY" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="6">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                         <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="UNLOCK_ALT" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="7">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                         <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="UNLOCK" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <TextField id="inputText1" fx:id="labConfirmPassword" disable="true" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="8" />
                                                      <TextField id="inputText1" fx:id="labNewPassword" disable="true" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="7" />
                                                      <TextField id="inputText1" fx:id="labPassword" disable="true" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="6" />
                                                      <Label id="inputLabel1" maxHeight="30.0" maxWidth="130.0" minHeight="30.0" minWidth="130.0" stylesheets="@../styles/box.css" text="Confirm Password" GridPane.columnIndex="1" GridPane.rowIndex="8">
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="30.0" maxWidth="130.0" minHeight="30.0" minWidth="130.0" stylesheets="@../styles/box.css" text="new Password" GridPane.columnIndex="1" GridPane.rowIndex="7">
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="30.0" maxWidth="130.0" minHeight="30.0" minWidth="130.0" stylesheets="@../styles/box.css" text="Current Password" GridPane.columnIndex="1" GridPane.rowIndex="6">
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Button id="dark-blue" fx:id="editUserInfoButton" maxHeight="30.0" maxWidth="60.0" minHeight="30.0" minWidth="60.0" mnemonicParsing="false" onAction="#editUserInfo" stylesheets="@../styles/buttons.css" text="Edit" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                      </Button>
                                                      <Button id="dark-blue" fx:id="editPasswordInfoButton" maxHeight="30.0" maxWidth="60.0" minHeight="30.0" minWidth="60.0" mnemonicParsing="false" onAction="#editPasswordInfo" stylesheets="@../styles/buttons.css" text="Edit" GridPane.columnIndex="1" GridPane.rowIndex="9">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                      </Button>
                                                   </children>
                                                </GridPane>
                                             </children></AnchorPane>
                                    </content>
                                  </Tab>
                                </tabs>
                              </TabPane>
                              <GridPane GridPane.columnIndex="1" GridPane.rowIndex="2">
                                <columnConstraints>
                                  <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                                </columnConstraints>
                                <rowConstraints>
                                  <RowConstraints maxHeight="39.0" minHeight="39.0" prefHeight="39.0" vgrow="SOMETIMES" />
                                  <RowConstraints maxHeight="275.0" minHeight="275.0" prefHeight="275.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                  <RowConstraints maxHeight="572.0" minHeight="10.0" prefHeight="167.0" vgrow="SOMETIMES" />
                                    <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                </rowConstraints>
                                 <children>
                                    <Label id="profileBox" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.rowIndex="1" GridPane.rowSpan="4" />
                                    <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/header.css" />
                                    <ImageView fx:id="profileImage" fitHeight="180.0" fitWidth="180.0" pickOnBounds="true" preserveRatio="true" GridPane.rowIndex="1">
                                       <GridPane.margin>
                                          <Insets left="10.0" top="60.0" />
                                       </GridPane.margin>
                                       <image>
                                          <Image url="@../imgs/profile.png" />
                                       </image>
                                    </ImageView>
                                    <Button id="dark-blue" fx:id="editProfilePicButton" maxHeight="30.0" maxWidth="1.7976931348623157E308" minHeight="30.0" minWidth="65.0" mnemonicParsing="false" onAction="#editProfilePic" stylesheets="@../styles/buttons.css" text="Edit" GridPane.rowIndex="2">
                                       <GridPane.margin>
                                          <Insets left="10.0" right="10.0" />
                                       </GridPane.margin>
                                    </Button>
                                 </children>
                              </GridPane>
                           </children>
                        </GridPane>
                     </children></AnchorPane>
            </content>
          </Tab>
        </tabs>
      </TabPane>
      <GridPane layoutX="22.0" layoutY="5.0" maxHeight="51.0" maxWidth="1.7976931348623157E308" prefHeight="51.0" prefWidth="778.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
        <columnConstraints>
          <ColumnConstraints hgrow="SOMETIMES" maxWidth="170.0" minWidth="170.0" prefWidth="170.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="257.0" minWidth="10.0" prefWidth="139.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1000.0" minWidth="10.0" prefWidth="287.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="60.0" minWidth="60.0" prefWidth="60.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="60.0" minWidth="60.0" prefWidth="60.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="60.0" minWidth="60.0" prefWidth="60.0" />
          <ColumnConstraints hgrow="SOMETIMES" maxWidth="20.0" minWidth="8.0" prefWidth="20.0" />
        </columnConstraints>
        <rowConstraints>
          <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
        </rowConstraints>
         <children>
            <Label alignment="BASELINE_CENTER" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="40.0" prefWidth="78.0" stylesheets="@../styles/header.css" GridPane.columnIndex="3">    
            
            </Label>
            <Label alignment="BASELINE_CENTER" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" minWidth="-Infinity" stylesheets="@../styles/header.css" GridPane.columnIndex="4">
             
            </Label>    
            
            <Label alignment="BASELINE_CENTER" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" minWidth="-Infinity" stylesheets="@../styles/header.css" textFill="#e1dede" GridPane.columnIndex="5">
             
            </Label>
            <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/header.css" GridPane.columnIndex="1" />
            <Label alignment="CENTER" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="51.0" prefWidth="233.0" stylesheets="@../styles/header.css" text=" HealthPlus" textFill="#d7d6d6">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
               <graphic>
               
                   <FontAwesomeIconView fill="#ff6666" glyphName="PLUS_CIRCLE" glyphStyle="" size="20px" />
                 
               </graphic>
               <effect>
                  <Bloom threshold="0.84" />
               </effect> 
            </Label>
            <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="55.0" prefWidth="248.0" stylesheets="@../styles/header.css" GridPane.columnIndex="2" />
            <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/header.css" GridPane.columnIndex="6" />
            <Button id="logout" fx:id="logoutButton" maxWidth="40.0" minWidth="40.0" mnemonicParsing="false" onAction="#logout" stylesheets="@../styles/box.css" GridPane.columnIndex="5">
               <GridPane.margin>
                  <Insets left="10.0" />
               </GridPane.margin>
               <graphic>
                    
                   <FontAwesomeIconView fill="#ffffff" glyphName="SIGN_OUT" glyphStyle="" size="15px" />
                      
               </graphic>
               <cursor>
                  <Cursor fx:constant="HAND" />
               </cursor>
            </Button>
            <Button id="iconButton" fx:id="AllMessages" maxWidth="40.0" minWidth="40.0" mnemonicParsing="false" onAction="#showAllMessages" stylesheets="@../styles/box.css" GridPane.columnIndex="4">
               <graphic>
                    
                  <FontAwesomeIconView fill="#222" glyphName="ENVELOPE" glyphStyle="" size="15px" />
                   
                </graphic>
                <GridPane.margin>
                  <Insets left="10.0" />
               </GridPane.margin>
               <cursor>
                  <Cursor fx:constant="HAND" />
               </cursor>
            </Button>
            <Button id="iconButton" fx:id="showUserButton" maxWidth="40.0" minWidth="40.0" mnemonicParsing="false" onAction="#showUser" stylesheets="@../styles/box.css" GridPane.columnIndex="3">
               <GridPane.margin>
                  <Insets left="10.0" />
               </GridPane.margin>
                <graphic>
                    
                    <FontAwesomeIconView fill="#222" glyphName="USER" glyphStyle="" size="15px" />
                      
                </graphic>
               <cursor>
                  <Cursor fx:constant="HAND" />
               </cursor>
               
            </Button>
            </children>
      </GridPane>  
    </children>
</fx:root>
