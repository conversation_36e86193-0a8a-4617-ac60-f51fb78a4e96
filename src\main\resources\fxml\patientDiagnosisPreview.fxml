<?xml version="1.0" encoding="UTF-8"?>
<!--
<?import javafx.scene.chart.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.effect.*?>
<?import javafx.scene.paint.*?>
<?import javafx.scene.text.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.web.*?>
<?import java.lang.*?>
<?import java.util.*?>
<?import javafx.scene.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import de.jensd.fx.glyphs.*?>
<?import de.jensd.fx.glyphs.materialicons.*?>
<?import de.jensd.fx.glyphs.fontawesome.*?>
<?import de.jensd.fx.glyphs.materialdesignicons.*?>
<?import de.jensd.fx.glyphs.octicons.*?>
<?import de.jensd.fx.glyphs.weathericons.*?>
<?import jfxtras.scene.control.*?>
<?import jfxtras.scene.control.agenda.*?>
<?import org.controlsfx.glyphfont.*?>
<?import impl.org.controlsfx.autocompletion.*?>
<?import javafx.collections.FXCollections?>
<?import javafx.scene.control.cell.PropertyValueFactory?>
<?import javafx.scene.control.cell.*?>
<?import javafx.collections.*?>
<?import Doctor.*?>

<AnchorPane id="anchor" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="180.0" prefWidth="400.0" stylesheets="@../styles/popup.css" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1" fx:controller="Doctor.DoctorController">
   <children>
      <Label layoutX="145.0" layoutY="251.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0" />
      <GridPane layoutX="129.0" layoutY="158.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
        <columnConstraints>
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="100.0" prefWidth="600.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="600.0" prefWidth="600.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="100.0" prefWidth="600.0" />
        </columnConstraints>
        <rowConstraints>
            <RowConstraints maxHeight="1.7976931348623157E308" minHeight="100.0" prefHeight="100.0" vgrow="SOMETIMES" />
          <RowConstraints maxHeight="500.0" minHeight="500.0" prefHeight="500.0" vgrow="SOMETIMES" />
            <RowConstraints maxHeight="1.7976931348623157E308" minHeight="100.0" prefHeight="100.0" vgrow="SOMETIMES" />
        </rowConstraints>
         <children>
            <Label id="box" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/popup.css" GridPane.columnIndex="1" GridPane.rowIndex="1" />
            <GridPane maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.rowIndex="1">
              <columnConstraints>
                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="20.0" minWidth="20.0" prefWidth="20.0" />
                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="600.0" minWidth="10.0" prefWidth="600.0" />
                <ColumnConstraints hgrow="SOMETIMES" maxWidth="70.0" minWidth="70.0" prefWidth="70.0" />
                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="20.0" minWidth="20.0" prefWidth="20.0" />
              </columnConstraints>
              <rowConstraints>
                <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="30.0" minHeight="30.0" prefHeight="30.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="120.0" minHeight="120.0" prefHeight="120.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="160.0" minHeight="60.0" prefHeight="160.0" vgrow="SOMETIMES" />
                <RowConstraints maxHeight="60.0" minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
              </rowConstraints>
               <children>
                
                  <Button id="dark-blue" fx:id="closePatientDoc" maxHeight="30.0" maxWidth="70.0" minHeight="30.0" minWidth="70.0" mnemonicParsing="false" onAction="#closeButtonActionDoc" stylesheets="@../styles/buttons.css" text="Cancel" GridPane.columnIndex="2" GridPane.rowIndex="9" />
                 
                   <Label fx:id="patientNamePreview" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.rowIndex="1">
                     <GridPane.margin>
                        <Insets />
                     </GridPane.margin>
                     <font>
                        <Font name="System Bold" size="13.0" />
                     </font>
                  </Label>
                  <Label textFill="RED" GridPane.columnIndex="1" GridPane.rowIndex="9">
                     <GridPane.margin>
                        <Insets left="30.0" />
                     </GridPane.margin>
                     <font>
                        <Font name="System Italic" size="10.0" />
                     </font>
                  </Label>
                  <Label fx:id="patientGenderPreview" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.rowIndex="2">
                     <font>
                        <Font name="System Bold" size="13.0" />
                     </font>
                  </Label>
                  <Label fx:id="patientAgePreview" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.rowIndex="3">
                     <font>
                        <Font name="System Bold" size="13.0" />
                     </font>
                  </Label>
                  <Label fx:id="patientGenderPreview1" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Diagnosis" GridPane.columnIndex="1" GridPane.rowIndex="5">
                     <font>
                        <Font name="System Bold" size="13.0" />
                     </font>
                  </Label>
                  <TextArea id="previewBox" fx:id="patientDiagnosisPreview" editable="false" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/box.css" wrapText="true" GridPane.columnIndex="1" GridPane.columnSpan="2" GridPane.rowIndex="6" />
                  <GridPane GridPane.columnIndex="1" GridPane.columnSpan="2" GridPane.rowIndex="8">
                    <columnConstraints>
                      <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="20.0" minWidth="20.0" prefWidth="20.0" />
                        <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                    </columnConstraints>
                    <rowConstraints>
                      <RowConstraints maxHeight="30.0" minHeight="30.0" prefHeight="30.0" vgrow="SOMETIMES" />
                      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                    </rowConstraints>
                     <children>
                        <Label fx:id="patientGenderPreview11" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Prescription">
                           <font>
                              <Font name="System Bold" size="13.0" />
                           </font>
                        </Label>
                        <Label fx:id="patientGenderPreview111" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Tests" GridPane.columnIndex="2">
                           <font>
                              <Font name="System Bold" size="13.0" />
                           </font>
                        </Label>
                        <ListView id="previewBox" fx:id="patientPrescriptionPreview" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/box.css" GridPane.rowIndex="1" />
                        <ListView id="previewBox" fx:id="patientTestPreview" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                     </children>
                  </GridPane>
                  <Label fx:id="previewDate" alignment="CENTER_RIGHT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.columnSpan="2" GridPane.rowIndex="1">
                     <font>
                        <Font name="System Bold" size="13.0" />
                     </font>
                  </Label>
               </children>
            </GridPane>
         </children>
      </GridPane>
   </children>
</AnchorPane>
-->
