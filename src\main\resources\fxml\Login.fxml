<?xml version="1.0" encoding="UTF-8"?>

<?import java.net.*?>
<?import javafx.scene.chart.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.effect.*?>
<?import javafx.scene.paint.*?>
<?import javafx.scene.text.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.web.*?>
<?import java.lang.*?>
<?import java.util.*?>
<?import javafx.scene.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import de.jensd.fx.glyphs.*?>
<?import de.jensd.fx.glyphs.materialicons.*?>
<?import de.jensd.fx.glyphs.fontawesome.*?>
<?import de.jensd.fx.glyphs.materialdesignicons.*?>
<?import de.jensd.fx.glyphs.octicons.*?>
<?import de.jensd.fx.glyphs.weathericons.*?>
<?import jfxtras.scene.control.*?>
<?import jfxtras.scene.control.agenda.*?>
<?import org.controlsfx.glyphfont.*?>
<?import impl.org.controlsfx.autocompletion.*?>
<?import javafx.collections.FXCollections?>
<?import javafx.scene.control.cell.PropertyValueFactory?>
<?import javafx.scene.control.cell.*?>
<?import javafx.collections.*?>
<?import com.hms.hms_test_2.*?>

<fx:root id="background" type="javafx.scene.layout.AnchorPane" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">    
    <children>
      <GridPane layoutX="180.0" layoutY="85.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
        <columnConstraints>
          <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="500.0" minWidth="500.0" prefWidth="500.0" />
          <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
        </columnConstraints>
        <rowConstraints>
          <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
          <RowConstraints maxHeight="260.0" minHeight="260.0" prefHeight="260.0" vgrow="SOMETIMES" />
          <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
        </rowConstraints>
         <children>
            <Label id="loginBox" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/login.css" GridPane.columnIndex="1" GridPane.rowIndex="1" />
            <GridPane GridPane.columnIndex="1" GridPane.rowIndex="1">
              <columnConstraints>
                <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
              </columnConstraints>
              <rowConstraints>
                <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
              </rowConstraints>
               <children>
                  <Label id="borderTop" alignment="CENTER" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/login.css" text="HEALTH PLUS LOGIN" textFill="WHITE">
                     <GridPane.margin>
                        <Insets left="1.0" right="1.0" top="1.0" />
                     </GridPane.margin>
                     <font>
                        <Font name="System Bold" size="17.0" />
                     </font>
                  </Label>
                  <Label id="borderBottom" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/login.css" GridPane.rowIndex="2">
                     <GridPane.margin>
                        <Insets bottom="1.0" left="1.0" right="1.0" />
                     </GridPane.margin>
                  </Label>
                  <GridPane GridPane.rowIndex="1">
                    <columnConstraints>
                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="160.0" minWidth="160.0" prefWidth="160.0" />
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="120.0" minWidth="120.0" prefWidth="120.0" />
                      <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="90.0" minWidth="90.0" prefWidth="90.0" />
                    </columnConstraints>
                    <rowConstraints>
                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                        <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                    </rowConstraints>
                     <children>
                        <Label id="inputLabel11" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Username" GridPane.columnIndex="1" GridPane.rowIndex="1">
                           <GridPane.margin>
                              <Insets left="30.0" />
                           </GridPane.margin>
                        </Label>
                        <Label id="inputLabel11" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Password" GridPane.columnIndex="1" GridPane.rowIndex="2">
                           <GridPane.margin>
                              <Insets left="30.0" />
                           </GridPane.margin>
                        </Label>
                        <TextField id="inputText2" fx:id="username" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.columnSpan="2" GridPane.rowIndex="1">
                           <GridPane.margin>
                              <Insets right="10.0" />
                           </GridPane.margin>
                        </TextField>
                        <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="1">
                            <graphic>
                                
                                <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="USER" textFill="#333333" />
                                 
                            </graphic>
                        </Label>    
                        <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="2">
                            <graphic>
                                
                                <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="KEY" textFill="#333333" />
                                 
                            </graphic>
                        </Label>    
                        <Label alignment="CENTER" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" GridPane.rowSpan="4">
                            <graphic>
                                    <Glyph fontFamily="FontAwesome" fontSize="100.0" icon="HEARTBEAT" textFill="#333333" />
                            </graphic>
                           <GridPane.margin>
                              <Insets bottom="20.0" />
                           </GridPane.margin>
                        </Label>    
                        <Button id="dark-blue" maxHeight="30.0" maxWidth="80.0" mnemonicParsing="false" onAction="#login" stylesheets="@../styles/buttons.css" text="Login" GridPane.columnIndex="3" GridPane.rowIndex="3" />
                        <PasswordField id="inputText2" fx:id="password" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.columnSpan="2" GridPane.rowIndex="2">
                           <GridPane.margin>
                              <Insets right="10.0" />
                           </GridPane.margin>
                        </PasswordField>
                     </children>
                  </GridPane>
               </children>
            </GridPane>
         </children>
      </GridPane>
   </children>
   <stylesheets>
      <URL value="@../styles/box.css" />
      <URL value="@../styles/login.css" />
   </stylesheets>
</fx:root>
