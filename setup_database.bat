@echo off
echo Setting up HealthPlus Database...
echo.
echo This script will:
echo 1. Create the test_HMS2 database
echo 2. Import the database schema and data
echo.
echo Please enter your MySQL root password when prompted.
echo.

REM Create the database
echo Creating database test_HMS2...
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS test_HMS2;"

if %ERRORLEVEL% NEQ 0 (
    echo Error: Failed to create database. Please check your MySQL credentials.
    pause
    exit /b 1
)

echo Database created successfully!
echo.

REM Import the database schema and data
echo Importing database schema and data...
mysql -u root -p test_HMS2 < database\hms_db.sql

if %ERRORLEVEL% NEQ 0 (
    echo Error: Failed to import database. Please check the SQL file.
    pause
    exit /b 1
)

echo Database setup completed successfully!
echo.
echo Default login credentials:
echo - Admin: user021 / 1234
echo - Doctor: user001 / 1234
echo - Pharmacist: user016 / 1234
echo - Lab Assistant: user012 / 1234
echo - Receptionist: user018 / 1234
echo - Cashier: user020 / 1234
echo.
pause
