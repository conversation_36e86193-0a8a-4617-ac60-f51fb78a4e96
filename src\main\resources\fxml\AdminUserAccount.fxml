<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.chart.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.effect.*?>
<?import javafx.scene.paint.*?>
<?import javafx.scene.text.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.web.*?>
<?import java.lang.*?>
<?import java.util.*?>
<?import javafx.scene.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import de.jensd.fx.glyphs.*?>
<?import de.jensd.fx.glyphs.materialicons.*?>
<?import de.jensd.fx.glyphs.fontawesome.*?>
<?import de.jensd.fx.glyphs.materialdesignicons.*?>
<?import de.jensd.fx.glyphs.octicons.*?>
<?import de.jensd.fx.glyphs.weathericons.*?>
<?import jfxtras.scene.control.*?>
<?import jfxtras.scene.control.agenda.*?>
<?import org.controlsfx.glyphfont.*?>
<?import impl.org.controlsfx.autocompletion.*?>
<?import javafx.collections.FXCollections?>
<?import javafx.scene.control.cell.PropertyValueFactory?>
<?import javafx.scene.control.cell.*?>
<?import javafx.collections.*?>
<?import Pharmacist.*?>

<fx:root id="anchor" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="600.0" prefWidth="900.0" stylesheets="@../styles/popup.css" type="javafx.scene.layout.AnchorPane" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">   <children>
      <Label layoutX="145.0" layoutY="251.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0" />
      <GridPane layoutX="129.0" layoutY="158.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
        <columnConstraints>
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="50.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="500.0" minWidth="500.0" prefWidth="500.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="50.0" prefWidth="50.0" />
        </columnConstraints>
        <rowConstraints>
            <RowConstraints maxHeight="1.7976931348623157E308" minHeight="20.0" prefHeight="50.0" vgrow="SOMETIMES" />
          <RowConstraints maxHeight="600.0" minHeight="-Infinity" prefHeight="600.0" vgrow="SOMETIMES" />
            <RowConstraints maxHeight="1.7976931348623157E308" minHeight="20.0" prefHeight="50.0" vgrow="SOMETIMES" />
        </rowConstraints>
         <children>
            <Label id="box" fx:id="tableHeader" alignment="TOP_LEFT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/popup.css" text="Users" GridPane.columnIndex="1" GridPane.rowIndex="1">
               <font>
                  <Font name="System Bold" size="16.0" />
               </font></Label>
            <GridPane GridPane.columnIndex="1" GridPane.rowIndex="1">
              <columnConstraints>
                  <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" />
                <ColumnConstraints hgrow="SOMETIMES" maxWidth="75.0" minWidth="75.0" prefWidth="75.0" />
              </columnConstraints>
              <rowConstraints>
                <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                  <RowConstraints maxHeight="1.7976931348623157E308" minHeight="-Infinity" vgrow="SOMETIMES" />
                <RowConstraints maxHeight="60.0" minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
              </rowConstraints>
               <children>
                  <Pagination fx:id="userDetailPagination" maxPageIndicatorCount="5" pageCount="10" prefHeight="200.0" prefWidth="200.0" GridPane.columnSpan="2" GridPane.rowIndex="1" GridPane.rowSpan="2" />
                  <Button id="transparentButton" fx:id="closeAccounts" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" mnemonicParsing="false" onAction="#closeViewAccounts" stylesheets="@../styles/box.css" GridPane.columnIndex="1">
                     <GridPane.margin>
                        <Insets bottom="10.0" left="40.0" />
                     </GridPane.margin>
                     <graphic>
                                             
                         <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="TIMES_CIRCLE" textFill="#303641" />

                    </graphic>
                     <cursor>
                        <Cursor fx:constant="HAND" />
                     </cursor>
                  </Button>
                  <GridPane fx:id="userDetailGrid" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="355.0" prefWidth="750.0" GridPane.columnSpan="2" GridPane.rowIndex="1">
                     <children>
                        <GridPane GridPane.columnIndex="1">
                           <children>
                              <Label id="profileGridInfo" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowSpan="2" />
                              <ImageView fx:id="userImage1" fitHeight="80.0" fitWidth="80.0" pickOnBounds="true" preserveRatio="true">
                                 <image>
                                    <Image url="@../imgs/profile.png" />
                                 </image>
                                 <GridPane.margin>
                                    <Insets bottom="5.0" left="25.0" top="5.0" />
                                 </GridPane.margin>
                              </ImageView>
                              <GridPane GridPane.columnIndex="1">
                                <columnConstraints>
                                  <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                                </columnConstraints>
                                <rowConstraints>
                                  <RowConstraints maxHeight="1.7976931348623157E308" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                                  <RowConstraints maxHeight="25.0" minHeight="25.0" prefHeight="25.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="25.0" minHeight="25.0" prefHeight="25.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="25.0" minHeight="25.0" prefHeight="25.0" vgrow="SOMETIMES" />
                                </rowConstraints>
                                 <children>
                                    <Label fx:id="userName1" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="First Name Last Name">
                                       <font>
                                          <Font name="System Bold" size="15.0" />
                                       </font>
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <Label fx:id="userId1" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="943562172V" GridPane.rowIndex="1">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <Label fx:id="userMobile1" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="0712453714" GridPane.rowIndex="2">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <Label fx:id="userEmail1" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="<EMAIL>" GridPane.rowIndex="3">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                    </Label>
                                 </children>
                              </GridPane>
                              <Label id="imageBorder" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnSpan="2" GridPane.rowSpan="2">
                                 <GridPane.margin>
                                    <Insets left="10.0" />
                                 </GridPane.margin>
                              </Label>
                              <Button id="dark-blue2" fx:id="msg1" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" mnemonicParsing="false" onAction="#sendMsg1" stylesheets="@../styles/buttons.css" GridPane.columnIndex="2">
                                 <graphic>
                                    <Glyph fontFamily="FontAwesome" fontSize="20.0" icon="ENVELOPE" textFill="#fff" />
                                 </graphic>
                                 <GridPane.margin>
                                    <Insets right="10.0" />
                                 </GridPane.margin>
                              </Button>
                           </children>
                           <columnConstraints>
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="120.0" minWidth="120.0" prefWidth="120.0" />
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="60.0" minWidth="60.0" prefWidth="60.0" />
                           </columnConstraints>
                           <rowConstraints>
                              <RowConstraints maxHeight="100.0" minHeight="100.0" prefHeight="100.0" vgrow="SOMETIMES" />
                              <RowConstraints />
                           </rowConstraints>
                        </GridPane>
                        <GridPane GridPane.columnIndex="1" GridPane.rowIndex="2">
                           <children>
                              <Label id="profileGridInfo" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowSpan="2" />
                              <ImageView fx:id="userImage2" fitHeight="80.0" fitWidth="80.0" pickOnBounds="true" preserveRatio="true">
                                 <image>
                                    <Image url="@../imgs/profile.png" />
                                 </image>
                                 <GridPane.margin>
                                    <Insets bottom="5.0" left="25.0" top="5.0" />
                                 </GridPane.margin>
                              </ImageView>
                              <GridPane GridPane.columnIndex="1">
                                 <children>
                                    <Label fx:id="userName2" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="First Name Last Name">
                                       <font>
                                          <Font name="System Bold" size="15.0" />
                                       </font>
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <Label fx:id="userId2" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="943562172V" GridPane.rowIndex="1">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <Label fx:id="userMobile2" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="0712453714" GridPane.rowIndex="2">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <Label fx:id="userEmail2" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="<EMAIL>" GridPane.rowIndex="3">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                    </Label>
                                 </children>
                                 <columnConstraints>
                                    <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                                 </columnConstraints>
                                 <rowConstraints>
                                    <RowConstraints maxHeight="1.7976931348623157E308" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="25.0" minHeight="25.0" prefHeight="25.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="25.0" minHeight="25.0" prefHeight="25.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="25.0" minHeight="25.0" prefHeight="25.0" vgrow="SOMETIMES" />
                                 </rowConstraints>
                              </GridPane>
                              <Label id="imageBorder" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnSpan="2" GridPane.rowSpan="2">
                                 <GridPane.margin>
                                    <Insets left="10.0" />
                                 </GridPane.margin>
                              </Label>
                              <Button id="dark-blue2" fx:id="msg2" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" mnemonicParsing="false" onAction="#sendMsg2" stylesheets="@../styles/buttons.css" GridPane.columnIndex="2">
                                 <graphic>
                                    <Glyph fontFamily="FontAwesome" fontSize="20.0" icon="ENVELOPE" textFill="#fff" />
                                 </graphic>
                                 <GridPane.margin>
                                    <Insets right="10.0" />
                                 </GridPane.margin>
                              </Button>
                           </children>
                           <columnConstraints>
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="120.0" minWidth="120.0" prefWidth="120.0" />
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="60.0" minWidth="60.0" prefWidth="60.0" />
                           </columnConstraints>
                           <rowConstraints>
                              <RowConstraints maxHeight="100.0" minHeight="100.0" prefHeight="100.0" vgrow="SOMETIMES" />
                              <RowConstraints />
                           </rowConstraints>
                        </GridPane>
                        <GridPane GridPane.columnIndex="1" GridPane.rowIndex="4">
                           <children>
                              <Label id="profileGridInfo" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowSpan="2" />
                              <ImageView fx:id="userImage3" fitHeight="80.0" fitWidth="80.0" pickOnBounds="true" preserveRatio="true">
                                 <image>
                                    <Image url="@../imgs/profile.png" />
                                 </image>
                                 <GridPane.margin>
                                    <Insets bottom="5.0" left="25.0" top="5.0" />
                                 </GridPane.margin>
                              </ImageView>
                              <GridPane GridPane.columnIndex="1">
                                 <children>
                                    <Label fx:id="userName3" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="First Name Last Name">
                                       <font>
                                          <Font name="System Bold" size="15.0" />
                                       </font>
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <Label fx:id="userId3" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="943562172V" GridPane.rowIndex="1">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <Label fx:id="userMobile3" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="0712453714" GridPane.rowIndex="2">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <Label fx:id="userEmail3" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="<EMAIL>" GridPane.rowIndex="3">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                    </Label>
                                 </children>
                                 <columnConstraints>
                                    <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                                 </columnConstraints>
                                 <rowConstraints>
                                    <RowConstraints maxHeight="1.7976931348623157E308" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="25.0" minHeight="25.0" prefHeight="25.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="25.0" minHeight="25.0" prefHeight="25.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="25.0" minHeight="25.0" prefHeight="25.0" vgrow="SOMETIMES" />
                                 </rowConstraints>
                              </GridPane>
                              <Label id="imageBorder" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnSpan="2" GridPane.rowSpan="2">
                                 <GridPane.margin>
                                    <Insets left="10.0" />
                                 </GridPane.margin>
                              </Label>
                              <Button id="dark-blue2" fx:id="msg3" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" mnemonicParsing="false" onAction="#sendMsg3" stylesheets="@../styles/buttons.css" GridPane.columnIndex="2">
                                 <graphic>
                                    <Glyph fontFamily="FontAwesome" fontSize="20.0" icon="ENVELOPE" textFill="#fff" />
                                 </graphic>
                                 <GridPane.margin>
                                    <Insets right="10.0" />
                                 </GridPane.margin>
                              </Button>
                           </children>
                           <columnConstraints>
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="120.0" minWidth="120.0" prefWidth="120.0" />
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="60.0" minWidth="60.0" prefWidth="60.0" />
                           </columnConstraints>
                           <rowConstraints>
                              <RowConstraints maxHeight="100.0" minHeight="100.0" prefHeight="100.0" vgrow="SOMETIMES" />
                              <RowConstraints />
                           </rowConstraints>
                        </GridPane>
                        <GridPane GridPane.columnIndex="1" GridPane.rowIndex="6">
                           <children>
                              <Label id="profileGridInfo" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowSpan="2" />
                              <ImageView fx:id="userImage4" fitHeight="80.0" fitWidth="80.0" pickOnBounds="true" preserveRatio="true">
                                 <image>
                                    <Image url="@../imgs/profile.png" />
                                 </image>
                                 <GridPane.margin>
                                    <Insets bottom="5.0" left="25.0" top="5.0" />
                                 </GridPane.margin>
                              </ImageView>
                              <GridPane GridPane.columnIndex="1">
                                 <children>
                                    <Label fx:id="userName4" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="First Name Last Name">
                                       <font>
                                          <Font name="System Bold" size="15.0" />
                                       </font>
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <Label fx:id="userId4" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="943562172V" GridPane.rowIndex="1">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <Label fx:id="userMobile4" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="0712453714" GridPane.rowIndex="2">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                    </Label>
                                    <Label fx:id="userEmail4" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="<EMAIL>" GridPane.rowIndex="3">
                                       <GridPane.margin>
                                          <Insets left="10.0" />
                                       </GridPane.margin>
                                    </Label>
                                 </children>
                                 <columnConstraints>
                                    <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                                 </columnConstraints>
                                 <rowConstraints>
                                    <RowConstraints maxHeight="1.7976931348623157E308" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="25.0" minHeight="25.0" prefHeight="25.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="25.0" minHeight="25.0" prefHeight="25.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="25.0" minHeight="25.0" prefHeight="25.0" vgrow="SOMETIMES" />
                                 </rowConstraints>
                              </GridPane>
                              <Label id="imageBorder" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnSpan="2" GridPane.rowSpan="2">
                                 <GridPane.margin>
                                    <Insets left="10.0" />
                                 </GridPane.margin>
                              </Label>
                              <Button id="dark-blue2" fx:id="msg4" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" mnemonicParsing="false" onAction="#sendMsg4" stylesheets="@../styles/buttons.css" GridPane.columnIndex="2">
                                 <graphic>
                                    <Glyph fontFamily="FontAwesome" fontSize="20.0" icon="ENVELOPE" textFill="#fff" />
                                 </graphic>
                                 <GridPane.margin>
                                    <Insets right="10.0" />
                                 </GridPane.margin>
                              </Button>
                           </children>
                           <columnConstraints>
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="120.0" minWidth="120.0" prefWidth="120.0" />
                              <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="60.0" minWidth="60.0" prefWidth="60.0" />
                           </columnConstraints>
                           <rowConstraints>
                              <RowConstraints maxHeight="100.0" minHeight="100.0" prefHeight="100.0" vgrow="SOMETIMES" />
                              <RowConstraints />
                           </rowConstraints>
                        </GridPane>
                     </children>
                     <columnConstraints>
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="100.0" minWidth="10.0" prefWidth="20.0" />
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="600.0" minWidth="10.0" prefWidth="510.0" />
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="0.0" minWidth="10.0" prefWidth="20.0" />
                     </columnConstraints>
                     <rowConstraints>
                        <RowConstraints maxHeight="100.0" minHeight="100.0" prefHeight="100.0" vgrow="SOMETIMES" />
                        <RowConstraints maxHeight="-Infinity" minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                        <RowConstraints maxHeight="100.0" minHeight="100.0" prefHeight="100.0" vgrow="SOMETIMES" />
                        <RowConstraints maxHeight="-Infinity" minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                        <RowConstraints maxHeight="100.0" minHeight="100.0" prefHeight="100.0" vgrow="SOMETIMES" />
                        <RowConstraints maxHeight="-Infinity" minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                        <RowConstraints maxHeight="100.0" minHeight="100.0" prefHeight="100.0" vgrow="SOMETIMES" />
                        <RowConstraints maxHeight="30.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                     </rowConstraints>
                  </GridPane>
               </children>
            </GridPane>
         </children>
      </GridPane>
   </children>
</fx:root>
