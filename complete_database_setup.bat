@echo off
echo HealthPlus Complete Database Setup
echo ===================================
echo.
echo This script will:
echo 1. Create the test_HMS2 database
echo 2. Create the 'heshan' user with password 'pass'
echo 3. Import all database tables and data
echo 4. Verify the setup
echo.
echo You will be prompted for your MySQL root password multiple times.
echo.
pause

echo Step 1: Creating database test_HMS2...
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS test_HMS2;"
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to create database.
    pause
    exit /b 1
)
echo Database created successfully!
echo.

echo Step 2: Creating user 'heshan' and granting permissions...
mysql -u root -p -e "CREATE USER IF NOT EXISTS 'heshan'@'localhost' IDENTIFIED BY 'pass'; GRANT ALL PRIVILEGES ON test_HMS2.* TO 'heshan'@'localhost'; GRANT CREATE, DROP ON *.* TO 'heshan'@'localhost'; FLUSH PRIVILEGES;"
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to create user or grant permissions.
    pause
    exit /b 1
)
echo User created and permissions granted successfully!
echo.

echo Step 3: Importing database schema and data...
mysql -u root -p test_HMS2 < database\hms_db.sql
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to import database.
    pause
    exit /b 1
)
echo Database imported successfully!
echo.

echo Step 4: Verifying setup...
mysql -u heshan -ppass -e "USE test_HMS2; SELECT COUNT(*) as user_count FROM sys_user;"
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Verification failed.
    pause
    exit /b 1
)
echo.

echo Step 5: Showing sample login credentials...
mysql -u heshan -ppass -e "USE test_HMS2; SELECT user_name, user_type, password FROM sys_user WHERE user_name IN ('user001', 'user012', 'user016', 'user018', 'user020', 'user021');"
echo.

echo ===================================
echo Setup completed successfully!
echo.
echo Default login credentials:
echo - Admin: user021 / 1234
echo - Doctor: user001 / 1234
echo - Pharmacist: user016 / 1234
echo - Lab Assistant: user012 / 1234
echo - Receptionist: user018 / 1234
echo - Cashier: user020 / 1234
echo.
echo You can now run: mvn javafx:run
echo ===================================
pause
