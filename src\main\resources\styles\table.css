.table-view{
   -fx-background-color: transparent;
}

.table-view:focused{
    -fx-background-color: transparent;
}

/* Spaltenköpfe
    Struktur column-header-background -> column-header */

.table-view .column-header-background{
    -fx-background-color: linear-gradient(#131313 0%, #424141 100%);
}

.table-view .column-header-background .label{
    -fx-background-color: transparent;
    -fx-text-fill: white;
}

.table-view .column-header {
    -fx-background-color: transparent;
}

.table-view .table-cell .text{
    -fx-text-fill: black;
}


.table-row-cell{
    -fx-background-color: -fx-table-cell-border-color, #ffffff; /*#616161;*/
    -fx-background-insets: 0, 0 0 1 0;
    -fx-padding: 0 0 0 5; /* 0 */
}

.table-row-cell:odd{
    -fx-background-color: -fx-table-cell-border-color, #ddd;
    -fx-background-insets: 0, 0 0 1 0;
    -fx-padding: 0 0 0 5; /* 0 */
}
/*
.table-row-cell:selected {
    -fx-background-color: #005797;
    -fx-background-insets: 0;
    -fx-background-radius: 1;
    -fx-text-fill: whitesmoke !important;
}

.table-view .table-cell:selected{
    -fx-text-fill: whitesmoke !important;
}
*/
.table-view > .virtual-flow > .scroll-bar:vertical,
.table-view > .virtual-flow > .scroll-bar:vertical > .track,
.table-view > .virtual-flow > .scroll-bar:vertical > .track-background, 
.table-view > .virtual-flow > .scroll-bar:horizontal,
.table-view > .virtual-flow > .scroll-bar:horizontal > .track,
.table-view > .virtual-flow > .scroll-bar:horizontal > .track-background {
    -fx-background-color: transparent;
}



.table-view > .virtual-flow > .scroll-bar > .increment-button, 
.table-view > .virtual-flow > .scroll-bar > .decrement-button {
    -fx-opacity: 0;
}

.table-view .filler{
    -fx-background-color: linear-gradient(#131313 0%, #424141 100%);
}

.table-row-cell {
    -fx-cell-size: 35px;
}

.table-row-cell:selected {
   -fx-background-color: steelblue; 
   -fx-text-background-color: white;
}

.table-column {
  -fx-alignment: CENTER-LEFT;
  
}