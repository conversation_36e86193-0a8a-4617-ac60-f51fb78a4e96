@echo off
echo HealthPlus Database Verification Script
echo ========================================
echo.

echo Step 1: Checking if test_HMS2 database exists...
mysql -u root -p -e "SHOW DATABASES LIKE 'test_HMS2';"
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Could not connect to MySQL or database doesn't exist.
    echo Please make sure MySQL is running and you have the correct root password.
    pause
    exit /b 1
)
echo.

echo Step 2: Checking if user 'heshan' exists...
mysql -u root -p -e "SELECT User, Host FROM mysql.user WHERE User = 'heshan';"
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Could not check users.
    pause
    exit /b 1
)
echo.

echo Step 3: Testing connection with application credentials...
mysql -u heshan -ppass -e "USE test_HMS2; SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'test_HMS2';"
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Cannot connect with application credentials (heshan/pass).
    echo This means either:
    echo 1. User 'heshan' doesn't exist
    echo 2. Password is incorrect
    echo 3. User doesn't have permissions on test_HMS2
    echo.
    echo Let's try to fix this...
    echo Creating user and granting permissions...
    mysql -u root -p -e "CREATE USER IF NOT EXISTS 'heshan'@'localhost' IDENTIFIED BY 'pass'; GRANT ALL PRIVILEGES ON test_HMS2.* TO 'heshan'@'localhost'; FLUSH PRIVILEGES;"
    echo.
    echo Retesting connection...
    mysql -u heshan -ppass -e "USE test_HMS2; SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'test_HMS2';"
    if %ERRORLEVEL% NEQ 0 (
        echo ERROR: Still cannot connect. Please check your MySQL setup.
        pause
        exit /b 1
    )
)
echo.

echo Step 4: Checking if sys_user table exists and has data...
mysql -u heshan -ppass -e "USE test_HMS2; SELECT COUNT(*) as user_count FROM sys_user;"
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: sys_user table doesn't exist or is empty.
    echo You need to import the database schema.
    echo Run: mysql -u root -p test_HMS2 < database\hms_db.sql
    pause
    exit /b 1
)
echo.

echo Step 5: Showing available users for login...
mysql -u heshan -ppass -e "USE test_HMS2; SELECT user_name, user_type, password FROM sys_user LIMIT 10;"
echo.

echo ========================================
echo Database verification completed successfully!
echo You can now run the application with: mvn javafx:run
echo ========================================
pause
