<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.chart.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.effect.*?>
<?import javafx.scene.paint.*?>
<?import javafx.scene.text.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.web.*?>
<?import java.lang.*?>
<?import java.util.*?>
<?import javafx.scene.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import de.jensd.fx.glyphs.*?>
<?import de.jensd.fx.glyphs.materialicons.*?>
<?import de.jensd.fx.glyphs.fontawesome.*?>
<?import de.jensd.fx.glyphs.materialdesignicons.*?>
<?import de.jensd.fx.glyphs.octicons.*?>
<?import de.jensd.fx.glyphs.weathericons.*?>
<?import jfxtras.scene.control.*?>
<?import jfxtras.scene.control.agenda.*?>
<?import org.controlsfx.glyphfont.*?>
<?import impl.org.controlsfx.autocompletion.*?>
<?import javafx.collections.FXCollections?>
<?import javafx.scene.control.cell.PropertyValueFactory?>
<?import javafx.scene.control.cell.*?>
<?import javafx.collections.*?>
<?import Doctor.*?>

<fx:root id="background" prefHeight="650.0" prefWidth="900.0" type="javafx.scene.layout.AnchorPane" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">    
    <children>
        <TabPane layoutX="213.0" layoutY="60.0" prefHeight="200.0" prefWidth="200.0" side="LEFT" stylesheets="@../styles/tabbedPane.css" tabClosingPolicy="UNAVAILABLE" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="40.0">
        <tabs>
          <Tab text="Dashboard">   
            <graphic>
                
                    <FontAwesomeIconView fx:id="tab_ico0" glyphName="TACHOMETER" glyphStyle="" size="50px" textAlignment="LEFT" />
                 
            </graphic>    
            <content>
              <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                     <children>
                        <GridPane layoutX="14.0" layoutY="14.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="560.0" prefWidth="610.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                          <columnConstraints>
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="552.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="30.0" minWidth="30.0" prefWidth="30.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="300.0" minWidth="250.0" prefWidth="250.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="30.0" minWidth="30.0" prefWidth="30.0" />
                          </columnConstraints>
                          <rowConstraints>
                            <RowConstraints maxHeight="6.0" minHeight="6.0" prefHeight="6.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="80.0" minHeight="80.0" prefHeight="80.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="275.0" minHeight="275.0" prefHeight="275.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="30.0" minHeight="30.0" prefHeight="30.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="400.0" minHeight="10.0" prefHeight="400.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                          </rowConstraints>
                           <children>
                              <Label fx:id="doctorUsername" text="Label" GridPane.columnIndex="3" GridPane.rowIndex="2" />
                              <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text=" Dashboard" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                <graphic>
                                    
                                    <Glyph fontFamily="FontAwesome" fontSize="30.0" icon="ARROW_CIRCLE_O_RIGHT" textFill="gray" />
                                      
                                </graphic>
                              </Label>    
                              
                              <Label id="box" alignment="TOP_LEFT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text=" Appointments" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                 <font>
                                    <Font name="System Bold" size="13.0" />
                                 </font>
                              </Label> 
                              
                              <Label id="box" alignment="TOP_LEFT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Today Appointments" GridPane.columnIndex="3" GridPane.rowIndex="2">
                                 <font>
                                    <Font name="System Bold" size="13.0" />
                                 </font>
                              </Label>
                              <Label fx:id="todayAppointments" alignment="CENTER" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="193.0" prefWidth="250.0" text="5" textFill="#222222" GridPane.columnIndex="3" GridPane.rowIndex="2">
                                 <font>
                                    <Font size="96.0" />
                                 </font>
                              </Label>
                              <Label id="box" alignment="TOP_LEFT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="115.0" prefWidth="530.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.columnSpan="3" GridPane.rowIndex="4">
                                <font>
                                    <Font name="System Bold" size="13.0" />
                                 </font>
                                 <opaqueInsets>
                                    <Insets />
                                 </opaqueInsets>
                              </Label>
                              <Label id="box" alignment="TOP_RIGHT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                    <graphic>
                                    
                                       <Glyph fontFamily="FontAwesome" fontSize="20.0" icon="CALENDAR" textFill="#222" />
                                      
                                    </graphic> 
                                  <opaqueInsets>
                                    <Insets />
                                 </opaqueInsets>
                                 <GridPane.margin>
                                    <Insets />
                                 </GridPane.margin>
                                 <padding>
                                    <Insets right="8.0" top="4.0" />
                                 </padding>
                              </Label>
                              <Label id="box" alignment="TOP_RIGHT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" GridPane.columnIndex="3" GridPane.rowIndex="2">
                                 <graphic>
                                    
                                        <Glyph fontFamily="FontAwesome" fontSize="20.0" icon="HAND_ALT_LEFT" textFill="#222" />
                                      
                                </graphic>
                                  <padding>
                                    <Insets bottom="8.0" left="8.0" right="8.0" top="4.0" />
                                 </padding>
                                 <GridPane.margin>
                                    <Insets />
                                 </GridPane.margin>
                              </Label>
                              
                              
                              <HBox maxWidth="1.7976931348623157E308" prefHeight="203.0" prefWidth="230.0" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                  <GridPane.margin>
                                    <Insets bottom="10.0" left="10.0" right="10.0" top="40.0" />
                                  </GridPane.margin>
                                 <children>  
                                     <Agenda id="id1" fx:id="appointmentTable" />
                                 </children>
                              </HBox>
                              <AreaChart id="patientSummary" fx:id="patientSummary" title="Patients" GridPane.columnIndex="1" GridPane.columnSpan="3" GridPane.rowIndex="4">
                                <xAxis>
                                  <CategoryAxis side="BOTTOM" />
                                </xAxis>
                                <yAxis>
                                  <NumberAxis fx:id="yaxis" side="LEFT" />
                                </yAxis>
                                 <GridPane.margin>
                                    <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                                 </GridPane.margin>
                              </AreaChart>
                              
                                  
                           </children>
                        </GridPane>
                     </children>
              </AnchorPane>   
            </content>
          </Tab>
          <Tab text="Patient">
            <graphic>
                
                   <FontAwesomeIconView fx:id="tab_ico1" glyphName="STETHOSCOPE" glyphStyle="" size="50px" textAlignment="LEFT" />
                    
            </graphic>  
            <content>
              <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                     <children>
                        <GridPane layoutX="62.0" layoutY="70.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="-0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                          <columnConstraints>
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="380.0" minWidth="10.0" prefWidth="195.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="30.0" minWidth="30.0" prefWidth="30.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="2000.0" minWidth="10.0" prefWidth="233.0" />
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                          </columnConstraints>
                          <rowConstraints>
                              <RowConstraints maxHeight="8.0" minHeight="8.0" prefHeight="8.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="80.0" minHeight="80.0" prefHeight="80.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="30.0" minHeight="30.0" prefHeight="30.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="1000.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                          </rowConstraints>
                           <children>
                              <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Patient" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                <graphic>
                                    
                                    <Glyph fontFamily="FontAwesome" fontSize="30.0" icon="ARROW_CIRCLE_O_RIGHT" textFill="gray" />
                                      
                                </graphic>
                              </Label>
                              <Label id="box" alignment="TOP_LEFT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Basic Details" GridPane.columnIndex="1" GridPane.rowIndex="2" GridPane.rowSpan="8">
                                 <font>
                                    <Font name="System Bold" size="13.0" />
                                 </font>
                              </Label>
                              <Label alignment="TOP_RIGHT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                 <graphic>
                                    
                                    <Glyph fontFamily="FontAwesome" fontSize="20.0" icon="INFO_CIRCLE" textFill="#222" />
                                      
                                </graphic>
                                  <padding>
                                    <Insets right="8.0" top="4.0" />
                                 </padding>
                              </Label>
                              <TextField id="inputText2" fx:id="patientFirstName" editable="false" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                 <GridPane.margin>
                                    <Insets left="100.0" right="15.0" />
                                 </GridPane.margin>
                              </TextField>
                              <TextField id="inputText2" fx:id="patientLastName" editable="false" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                 <GridPane.margin>
                                    <Insets left="100.0" right="15.0" />
                                 </GridPane.margin>
                              </TextField>
                              <TextField id="inputText2" fx:id="patientAge" editable="false" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="6">
                                 <GridPane.margin>
                                    <Insets left="100.0" right="15.0" />
                                 </GridPane.margin>
                              </TextField>
                              <TextField id="inputText2" fx:id="patientGender" editable="false" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="7">
                                 <GridPane.margin>
                                    <Insets left="100.0" right="15.0" />
                                 </GridPane.margin>
                              </TextField>
                              <TextField id="inputText2" fx:id="patientEmail" editable="false" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="8">
                                 <GridPane.margin>
                                    <Insets left="100.0" right="15.0" />
                                 </GridPane.margin>
                              </TextField>
                              <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.columnIndex="3" GridPane.rowIndex="2" GridPane.rowSpan="10" />
                              <Label id="inputLabel11" maxHeight="30.0" maxWidth="85.0" minHeight="30.0" minWidth="85.0" stylesheets="@../styles/box.css" text="First Name" textFill="#333333" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                 <GridPane.margin>
                                    <Insets left="15.0" />
                                 </GridPane.margin>
                              </Label>
                              <Label id="inputLabel11" maxHeight="30.0" maxWidth="85.0" minHeight="30.0" minWidth="85.0" stylesheets="@../styles/box.css" text="Last Name" textFill="#333333" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                 <GridPane.margin>
                                    <Insets left="15.0" />
                                 </GridPane.margin>
                              </Label>
                              <Label id="inputLabel11" maxHeight="30.0" maxWidth="85.0" minHeight="30.0" minWidth="85.0" stylesheets="@../styles/box.css" text="Age" textFill="#333333" GridPane.columnIndex="1" GridPane.rowIndex="6">
                                 <GridPane.margin>
                                    <Insets left="15.0" />
                                 </GridPane.margin>
                              </Label>
                              <Label id="inputLabel11" maxHeight="30.0" maxWidth="85.0" minHeight="30.0" minWidth="85.0" stylesheets="@../styles/box.css" text="Gender" textFill="#333333" GridPane.columnIndex="1" GridPane.rowIndex="7">
                                 <GridPane.margin>
                                    <Insets left="15.0" />
                                 </GridPane.margin>
                              </Label>
                              <Label id="inputLabel11" maxHeight="30.0" maxWidth="85.0" minHeight="30.0" minWidth="85.0" stylesheets="@../styles/box.css" text="E-mail" textFill="#333333" GridPane.columnIndex="1" GridPane.rowIndex="8">
                                 <GridPane.margin>
                                    <Insets left="15.0" />
                                 </GridPane.margin>
                              </Label>
                              <Label id="box" alignment="TOP_LEFT" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Diagnosis" GridPane.columnIndex="1" GridPane.rowIndex="11">
                                 <opaqueInsets>
                                    <Insets />
                                 </opaqueInsets>
                                 <font>
                                    <Font name="System Bold" size="13.0" />
                                 </font>
                              </Label>
                              <GridPane maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" GridPane.columnIndex="1" GridPane.rowIndex="11">
                                <columnConstraints>
                                  <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                                    <ColumnConstraints hgrow="SOMETIMES" maxWidth="170.0" minWidth="170.0" prefWidth="170.0" />
                                </columnConstraints>
                                <rowConstraints>
                                  <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                </rowConstraints>
                                 <children>
                                    <TextArea id="textBorder" fx:id="diagnosisText" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/box.css" wrapText="true" GridPane.columnSpan="2">
                                       <GridPane.margin>
                                          <Insets left="10.0" right="10.0" top="40.0" />
                                       </GridPane.margin>
                                    </TextArea>
                                    <Button id="dark-blue" fx:id="savePatientDiag" contentDisplay="CENTER" maxHeight="30.0" maxWidth="70.0" minHeight="30.0" minWidth="70.0" mnemonicParsing="false" onAction="#savePatientDiagnosis" stylesheets="@../styles/buttons.css" text="Save" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                       <GridPane.margin>
                                          <Insets left="90.0" />
                                       </GridPane.margin>
                                    </Button>
                                 </children>
                              </GridPane>
                              <TabPane prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/tabbedPane2.css" tabClosingPolicy="UNAVAILABLE" GridPane.columnIndex="3" GridPane.rowIndex="2" GridPane.rowSpan="10">
                                <tabs>
                                  <Tab text="Medical History">
                                    <content>
                                      <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                             <children>
                                                <TabPane layoutY="22.0" prefHeight="200.0" prefWidth="200.0" side="LEFT" stylesheets="@../styles/tabbedPane2.css" tabClosingPolicy="UNAVAILABLE" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="-0.0">
                                                  <tabs>
                                                    <Tab text="Allergies">
                                                      <content>
                                                        <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                                               <children>
                                                                  <Pagination fx:id="allergiesPagination" layoutX="14.0" layoutY="14.0" maxPageIndicatorCount="5" pageCount="5" prefHeight="200.0" prefWidth="200.0" AnchorPane.bottomAnchor="10.0" AnchorPane.leftAnchor="30.0" AnchorPane.rightAnchor="30.0" AnchorPane.topAnchor="80.0" />
                                                                  <ListView id="textBorder" fx:id="allergyView" layoutY="105.0" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/box.css" AnchorPane.bottomAnchor="100.0" AnchorPane.leftAnchor="30.0" AnchorPane.rightAnchor="30.0" AnchorPane.topAnchor="80.0" />
                                                                  <Label id="inputLabel01" alignment="CENTER" layoutX="30.0" layoutY="42.0" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" prefHeight="30.0" prefWidth="30.0" stylesheets="@../styles/box.css" AnchorPane.leftAnchor="30.0" AnchorPane.topAnchor="30.0">
                                                                    <graphic>  
                                                                        
                                                                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="STETHOSCOPE" textFill="#333" />
                                                                        
                                                                    </graphic>
                                                                  </Label>        
                                                                  <Label id="inputLabel11" layoutX="60.0" layoutY="42.0" maxHeight="30.0" maxWidth="70.0" minHeight="30.0" minWidth="70.0" stylesheets="@../styles/box.css" text="Allergy" AnchorPane.leftAnchor="60.0" AnchorPane.topAnchor="30.0" />
                                                                  <TextField id="inputText2" fx:id="allergyText" layoutX="34.0" layoutY="32.0" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" AnchorPane.leftAnchor="130.0" AnchorPane.rightAnchor="110.0" AnchorPane.topAnchor="30.0" />
                                                                  <Button id="dark-blue" alignment="CENTER" contentDisplay="CENTER" layoutX="138.0" layoutY="33.0" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" mnemonicParsing="false" onAction="#addPatientAllergies" stylesheets="@../styles/buttons.css" AnchorPane.rightAnchor="70.0" AnchorPane.topAnchor="30.0">
                                                                    <graphic>
                                                                     
                                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="PLUS" textFill="#eee" />
                                                                      
                                                                    </graphic>
                                                                  </Button>
                                                                  <Button id="dark-blue" fx:id="removeAllergyButton" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" mnemonicParsing="false" onAction="#removeAllergy" stylesheets="@../styles/buttons.css" text="Add" AnchorPane.rightAnchor="30.0" AnchorPane.topAnchor="30.0">
                                                                     <graphic>
                                                                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="MINUS" textFill="#eee" />
                                                                     </graphic>
                                                                  </Button>  
                                                               </children></AnchorPane>
                                                      </content>
                                                    </Tab>
                                                    <Tab text="History">
                                                      <content>
                                                        <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                                               <children>
                                                                  <Pagination fx:id="historyPagination" layoutX="22.0" layoutY="48.0" pageCount="20" prefHeight="200.0" prefWidth="200.0" AnchorPane.bottomAnchor="5.0" AnchorPane.leftAnchor="5.0" AnchorPane.rightAnchor="5.0" AnchorPane.topAnchor="5.0" />
                                                                  <GridPane fx:id="historyPane" layoutX="24.0" layoutY="101.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                                                    <columnConstraints>
                                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="30.0" minWidth="30.0" prefWidth="30.0" />
                                                                        <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="30.0" minWidth="30.0" prefWidth="30.0" />
                                                                    </columnConstraints>
                                                                    <rowConstraints>
                                                                        <RowConstraints maxHeight="30.0" minHeight="30.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                                      <RowConstraints fillHeight="false" maxHeight="1.7976931348623157E308" minHeight="50.0" prefHeight="70.0" vgrow="SOMETIMES" />
                                                                        <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                                        <RowConstraints fillHeight="false" maxHeight="1.7976931348623157E308" minHeight="50.0" prefHeight="70.0" vgrow="SOMETIMES" />
                                                                        <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                                        <RowConstraints fillHeight="false" maxHeight="1.7976931348623157E308" minHeight="50.0" prefHeight="70.0" vgrow="SOMETIMES" />
                                                                      <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                                    </rowConstraints>
                                                                     <children>
                                                                        <Label id="labelBox" fx:id="hisTime1" maxHeight="30.0" maxWidth="1.7976931348623157E308" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                                           <GridPane.margin>
                                                                              <Insets left="10.0" right="10.0" />
                                                                           </GridPane.margin>
                                                                        </Label>
                                                                        <Label id="labelBox" fx:id="hisTime2" maxHeight="30.0" maxWidth="1.7976931348623157E308" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                                                           <GridPane.margin>
                                                                              <Insets left="10.0" right="10.0" />
                                                                           </GridPane.margin>
                                                                        </Label>
                                                                        <Label id="labelBox" fx:id="hisTime3" maxHeight="30.0" maxWidth="1.7976931348623157E308" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                                                           <GridPane.margin>
                                                                              <Insets left="10.0" right="10.0" />
                                                                           </GridPane.margin>
                                                                        </Label>
                                                                        <TextArea id="textBorder" fx:id="hisDetail1" editable="false" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/box.css" wrapText="true" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                                           <GridPane.margin>
                                                                              <Insets bottom="15.0" left="10.0" right="10.0" top="5.0" />
                                                                           </GridPane.margin>
                                                                        </TextArea>
                                                                        <TextArea id="textBorder" fx:id="hisDetail2" editable="false" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/box.css" wrapText="true" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                                                           <GridPane.margin>
                                                                              <Insets bottom="15.0" left="10.0" right="10.0" top="5.0" />
                                                                           </GridPane.margin>
                                                                        </TextArea>
                                                                        <TextArea id="textBorder" fx:id="hisDetail3" editable="false" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/box.css" wrapText="true" GridPane.columnIndex="1" GridPane.rowIndex="6">
                                                                           <GridPane.margin>
                                                                              <Insets bottom="15.0" left="10.0" right="10.0" top="5.0" />
                                                                           </GridPane.margin>
                                                                        </TextArea>
                                                                     </children>
                                                                  </GridPane>
                                                               </children></AnchorPane>
                                                      </content>
                                                    </Tab>
                                                  </tabs>
                                                </TabPane>
                                             </children>
                                      </AnchorPane>
                                    </content>
                                  </Tab>
                                  <Tab text="Test Results">
                                    <content>
                                      <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                             <children>
                                                <Button id="dark-blue" fx:id="PatientTestResultsButton" alignment="CENTER" layoutX="170.0" layoutY="37.0" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" mnemonicParsing="false" onAction="#searchPatientTestResults" prefWidth="30.0" stylesheets="@../styles/buttons.css" AnchorPane.rightAnchor="70.0" AnchorPane.topAnchor="30.0">
                                                    <graphic>
                                    
                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="SEARCH" textFill="#eee" />

                                                    </graphic>
                                                </Button>    
                                                <Label id="inputLabel01" alignment="CENTER" layoutX="47.0" layoutY="37.0" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" AnchorPane.leftAnchor="30.0" AnchorPane.topAnchor="30.0">
                                                    <graphic>  
                                                        
                                                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="heartbeat" textFill="#333" />
                                                        
                                                    </graphic>
                                                </Label>    
                                                <Label id="inputLabel11" layoutX="104.0" layoutY="37.0" maxHeight="30.0" maxWidth="80.0" minHeight="30.0" minWidth="80.0" stylesheets="@../styles/box.css" text="Test ID" AnchorPane.leftAnchor="60.0" AnchorPane.topAnchor="30.0" />
                                                <TextField id="inputText2" fx:id="testID" layoutX="45.0" layoutY="32.0" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" AnchorPane.leftAnchor="140.0" AnchorPane.rightAnchor="110.0" AnchorPane.topAnchor="30.0" />
                                                <TableView id="reportTable" fx:id="testResultTable" layoutX="25.0" layoutY="92.0" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/tableHeader.css" AnchorPane.bottomAnchor="30.0" AnchorPane.leftAnchor="30.0" AnchorPane.rightAnchor="30.0" AnchorPane.topAnchor="90.0" />
                                                <Button id="dark-blue" alignment="CENTER" layoutX="270.0" layoutY="30.0" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" mnemonicParsing="false" onAction="#clearPatientTestResults" prefWidth="30.0" stylesheets="@../styles/buttons.css" text="Search" AnchorPane.rightAnchor="30.0" AnchorPane.topAnchor="30.0">
                                                    <graphic>
                                    
                                                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="ERASER" textFill="#eee" />

                                                     </graphic>
                                                </Button>    
                                             </children>
                                      </AnchorPane>
                                    </content>
                                  </Tab>
                                  <Tab text="Prescribe">
                                    <content>
                                      <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                             
                                         <children>
                                                <GridPane layoutX="30.0" layoutY="428.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                                  <columnConstraints>
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="30.0" minWidth="30.0" prefWidth="30.0" />
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="80.0" minWidth="10.0" prefWidth="100.0" />
                                                    <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="100.0" />
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="70.0" minWidth="10.0" prefWidth="100.0" />
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="85.0" minWidth="10.0" prefWidth="100.0" />
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="65.0" minWidth="65.0" prefWidth="65.0" />
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="30.0" minWidth="30.0" prefWidth="30.0" />
                                                  </columnConstraints>
                                                  <rowConstraints>
                                                      <RowConstraints maxHeight="30.0" minHeight="30.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="30.0" minHeight="30.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                                                    <RowConstraints maxHeight="1.7976931348623157E308" minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                    <RowConstraints maxHeight="30.0" minHeight="30.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="30.0" minHeight="30.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="400.0" minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                    <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                  </rowConstraints>
                                                   <children>

                                                        <TextField id="inputLabel11" fx:id="txtAuto" maxHeight="30.0" minHeight="30.0" onKeyReleased="#checkForBrands" prefHeight="30.0" prefWidth="52.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                                                        <Button id="dark-blue" fx:id="addDrug" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" mnemonicParsing="false" onAction="#addDrugtoPresc" stylesheets="@../styles/buttons.css" GridPane.columnIndex="5" GridPane.rowIndex="1">
                                                            <graphic>
                                                                     
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="PLUS" textFill="#eee" />

                                                            </graphic>  
                                                        </Button>        
                                                        <ComboBox id="inputText3" fx:id="unit" maxHeight="30.0" maxWidth="75.0" minHeight="30.0" minWidth="30.0" prefWidth="50.0" stylesheets="@../styles/box.css" GridPane.columnIndex="4" GridPane.rowIndex="1">
                                                            <items>
                                                                <FXCollections fx:factory="observableArrayList">
                                                               <String fx:value="mg" />
                                                               <String fx:value="g" />
                                                               <String fx:value="ml" />
                                                               <String fx:value="" />
                                                                </FXCollections>
                                                              </items>
                                                         <GridPane.margin>
                                                            <Insets right="10.0" />
                                                         </GridPane.margin>
                                                        </ComboBox>        
                                                        <TextField id="inputLabel2" fx:id="amount" maxHeight="30.0" maxWidth="70.0" minHeight="30.0" minWidth="30.0" onKeyReleased="#checkAmount" stylesheets="@../styles/box.css" GridPane.columnIndex="3" GridPane.rowIndex="1" />

                                                       
                                                         <ListView id="textBorder" fx:id="prescription" prefHeight="307.0" prefWidth="220.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.columnSpan="5" GridPane.rowIndex="3" />
                                                      <ListView id="textBorder" fx:id="prescription1" prefHeight="307.0" prefWidth="220.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.columnSpan="5" GridPane.rowIndex="7" />
                                                      <Button id="dark-blue" fx:id="addTest1" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" mnemonicParsing="false" onAction="#addTesttoPresc" stylesheets="@../styles/buttons.css" GridPane.columnIndex="5" GridPane.rowIndex="5">
                                                            <graphic>
                                                                     
                                                                <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="PLUS" textFill="#eee" />

                                                            </graphic>
                                                      </Button>        
                                                      <TextField id="inputText3" fx:id="txtAuto1" maxHeight="30.0" minHeight="30.0" prefHeight="30.0" prefWidth="52.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.columnSpan="3" GridPane.rowIndex="5">
                                                         <GridPane.margin>
                                                            <Insets right="10.0" />
                                                         </GridPane.margin>
                                                      </TextField>
                                                      <GridPane GridPane.columnIndex="2" GridPane.columnSpan="4" GridPane.rowIndex="8">
                                                        <columnConstraints>
                                                          <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="100.0" />
                                                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                                                          <ColumnConstraints hgrow="SOMETIMES" maxWidth="65.0" minWidth="65.0" prefWidth="65.0" />
                                                        </columnConstraints>
                                                        <rowConstraints>
                                                          <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                        </rowConstraints>
                                                         <children>
                                                              <Button id="dark-blue" maxHeight="30.0" maxWidth="65.0" minHeight="30.0" minWidth="65.0" mnemonicParsing="false" stylesheets="@../styles/buttons.css" text="Print" GridPane.columnIndex="2" />
                                                              <Button id="dark-blue" fx:id="clearPresc" maxHeight="30.0" maxWidth="40.0" minHeight="30.0" minWidth="40.0" mnemonicParsing="false" onAction="#clearPrescription" stylesheets="@../styles/buttons.css" GridPane.columnIndex="1">
                                                                  <graphic>   
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="ERASER" textFill="#eee" />
                                                                  </graphic>
                                                              </Button>        
                                                         </children>
                                                      </GridPane>
                                                      <Label id="inputLabel01" alignment="CENTER" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Medicine" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                                                      <Label id="inputLabel01" alignment="CENTER" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Tests" GridPane.columnIndex="1" GridPane.rowIndex="5" />
                                                      <Button id="dark-blue" fx:id="removeDrug" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" mnemonicParsing="false" onAction="#removeDrugPresc" stylesheets="@../styles/buttons.css" text="Add" GridPane.columnIndex="5" GridPane.rowIndex="1">
                                                         <GridPane.margin>
                                                            <Insets left="35.0" />
                                                         </GridPane.margin>
                                                         <graphic>
                                                                     
                                                                <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="MINUS" textFill="#eee" />

                                                            </graphic>
                                                      </Button>
                                                      <Button id="dark-blue" fx:id="removeTest" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" mnemonicParsing="false" onAction="#removeTestPresc" stylesheets="@../styles/buttons.css" text="Add" GridPane.columnIndex="5" GridPane.rowIndex="5">
                                                         <GridPane.margin>
                                                            <Insets left="35.0" />
                                                         </GridPane.margin>
                                                         <graphic>
                                                                     
                                                                <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="MINUS" textFill="#eee" />

                                                            </graphic>
                                                      </Button>
                                                   </children>
                                                </GridPane>
                                         </children>
                                               
                                      </AnchorPane>
                                    </content>
                                  </Tab>
                                </tabs>
                              </TabPane>
                              <Button id="dark-blue" fx:id="newPatientDoc" maxHeight="30.0" maxWidth="1.7976931348623157E308" minHeight="30.0" mnemonicParsing="false" onAction="#searchNewPatientDoc" stylesheets="@../styles/buttons.css" text="Search Patient" GridPane.columnIndex="1" GridPane.rowIndex="9">
                                 <GridPane.margin>
                                    <Insets left="65.0" right="15.0" />
                                 </GridPane.margin>
                              </Button>
                              <TextField id="inputText2" fx:id="patientSearchValue" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                 <GridPane.margin>
                                    <Insets left="165.0" right="15.0" />
                                 </GridPane.margin>
                              </TextField>
                              <Label id="inputLabel01" alignment="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                 <GridPane.margin>
                                    <Insets left="15.0" />
                                 </GridPane.margin>
                                 
                                 <graphic>   
                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="USER" textFill="#333" />
                                  </graphic>
                                  
                              </Label>
                              <ComboBox id="inputLabel11" fx:id="searchTypePatientDoctor" maxHeight="30.0" maxWidth="120.0" minHeight="30.0" minWidth="120.0" prefHeight="30.0" prefWidth="120.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                 <GridPane.margin>
                                    <Insets left="45.0" />
                                 </GridPane.margin>
                                 <items>
                                    <FXCollections fx:factory="observableArrayList">
                                   <String fx:value="Patient ID" />
                                   <String fx:value="Name" />
                                   <String fx:value="NIC" />
                                    </FXCollections>
                                  </items>
                              </ComboBox>
                              <Button id="dark-blue" fx:id="clearButton" maxHeight="30.0" maxWidth="40.0" minHeight="30.0" mnemonicParsing="false" onAction="#clearPatient" stylesheets="@../styles/buttons.css" GridPane.columnIndex="1" GridPane.rowIndex="9">
                                 <GridPane.margin>
                                    <Insets left="15.0" />
                                 </GridPane.margin>
                                 <graphic>   
                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="ERASER" textFill="#eee" />
                                  </graphic>
                              </Button>
                           </children>
                        </GridPane>
                     </children></AnchorPane>
            </content>
          </Tab>
          <Tab text="Profile">
            <graphic>
                
                    <FontAwesomeIconView fx:id="tab_ico2" glyphName="USER_MD" glyphStyle="" size="50px" textAlignment="LEFT" />
                    
            </graphic>  
            <content>
              <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                     <children>
                        <GridPane layoutX="83.0" layoutY="77.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                          <columnConstraints>
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="200.0" minWidth="200.0" prefWidth="200.0" />
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="200.0" prefWidth="400.0" />
                              <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                          </columnConstraints>
                          <rowConstraints>
                            <RowConstraints maxHeight="8.0" minHeight="8.0" prefHeight="8.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="80.0" minHeight="80.0" prefHeight="80.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="100000.0" minHeight="200.0" prefHeight="200.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                          </rowConstraints>
                           <children>
                              <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" text="Profile" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                <graphic>  
                                     
                                    <Glyph fontFamily="FontAwesome" fontSize="30.0" icon="ARROW_CIRCLE_O_RIGHT" textFill="gray" />
                                    
                                </graphic>
                              </Label>
                              <TabPane maxHeight="1000.0" maxWidth="1000.0" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/tabbedPane3.css" tabClosingPolicy="UNAVAILABLE" GridPane.columnIndex="2" GridPane.rowIndex="2">
                                <tabs>
                                  <Tab text="Basic Details">
                                    <content>
                                      <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                             <children>
                                                <GridPane layoutX="66.0" layoutY="69.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                                  <columnConstraints>
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                                                    <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="253.0" />
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="50.0" prefWidth="100.0" />
                                                  </columnConstraints>
                                                  <rowConstraints>
                                                      <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                    <RowConstraints maxHeight="220.0" minHeight="220.0" prefHeight="220.0" vgrow="SOMETIMES" />
                                                    <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                    <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="1.7976931348623157E308" minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                                                  </rowConstraints>
                                                   <children>
                                                      <GridPane prefHeight="212.0" prefWidth="279.0" GridPane.columnIndex="1" GridPane.rowIndex="1" GridPane.rowSpan="7">
                                                        <columnConstraints>
                                                          <ColumnConstraints hgrow="SOMETIMES" maxWidth="206.0" minWidth="10.0" prefWidth="149.0" />
                                                          <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="136.0" />
                                                        </columnConstraints>
                                                        <rowConstraints>
                                                            <RowConstraints maxHeight="20.0" minHeight="20.0" prefHeight="20.0" vgrow="SOMETIMES" />
                                                          <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                          <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                          <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                            <RowConstraints maxHeight="1.7976931348623157E308" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                        </rowConstraints>
                                                         <children>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Name" GridPane.rowIndex="1">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" prefHeight="16.0" prefWidth="86.0" stylesheets="@../styles/box.css" text="Date Of Birth" GridPane.rowIndex="3">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="NIC" GridPane.rowIndex="2">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Age" GridPane.rowIndex="4">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Gender" GridPane.rowIndex="5">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Nationality" GridPane.rowIndex="6">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Mobile" GridPane.rowIndex="8">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Email" GridPane.rowIndex="9">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Address" GridPane.rowIndex="10">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <Label id="inputLabel1" maxHeight="30.0" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" text="Religion" GridPane.rowIndex="7">
                                                               <GridPane.margin>
                                                                  <Insets left="40.0" />
                                                               </GridPane.margin>
                                                               
                                                            </Label>
                                                            <TextField id="inputText1" fx:id="doctorName" disable="true" maxHeight="30.0" maxWidth="400.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                                                            <TextField id="inputText1" fx:id="doctorNIC" disable="true" maxHeight="30.0" maxWidth="400.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                                                            <TextField id="inputText1" fx:id="doctorAge" disable="true" maxHeight="30.0" maxWidth="400.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="4" />
                                                            <TextField id="inputText1" fx:id="doctorNationality" disable="true" maxHeight="30.0" maxWidth="400.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="6" />
                                                            <TextField id="inputText1" fx:id="doctorReligion" disable="true" maxHeight="30.0" maxWidth="400.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="7" />
                                                            <TextField id="inputText1" fx:id="doctorMobile" disable="true" maxHeight="30.0" maxWidth="400.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="8" />
                                                            <TextField id="inputText1" fx:id="doctorEmail" disable="true" maxHeight="30.0" maxWidth="400.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="9" />
                                                            <TextField id="inputText1" fx:id="doctorAddress" disable="true" maxHeight="30.0" maxWidth="400.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="10" />
                                                            <DatePicker id="inputText1" fx:id="doctorDOB" disable="true" maxHeight="30.0" maxWidth="400.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                                                            <ComboBox id="comboBox" fx:id="doctorGender" disable="true" maxHeight="30.0" maxWidth="400.0" minWidth="30.0" prefWidth="150.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                                                <items>
                                                                   <FXCollections fx:factory="observableArrayList">
                                                                       <String fx:value="Male" />
                                                                       <String fx:value="Female" />
                                                                    </FXCollections>
                                                                </items>
                                                            </ComboBox>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="1">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="USER" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="2">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="CERTIFICATE" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="3">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="CALENDAR" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="4">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="STREET_VIEW" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="5">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="MALE" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="6">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="BELL" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="7">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                                <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="BULLSEYE" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="8">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="MOBILE_PHONE" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="9">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                               <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="ENVELOPE" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.rowIndex="10">
                                                               <opaqueInsets>
                                                                  <Insets />
                                                               </opaqueInsets>
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                                <graphic>  
                                                                    
                                                                    <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="MAP_MARKER" textFill="#333" />
                                                                    
                                                                </graphic>
                                                            </Label>
                                                            <Button id="dark-blue" fx:id="editBasicInfoButton" maxHeight="30.0" maxWidth="60.0" minHeight="30.0" minWidth="60.0" mnemonicParsing="false" onAction="#editBasicInfo" stylesheets="@../styles/buttons.css" text="Edit" GridPane.rowIndex="11">
                                                               <GridPane.margin>
                                                                  <Insets left="10.0" />
                                                               </GridPane.margin>
                                                            </Button>
                                                         </children>
                                                      </GridPane>
                                                   </children>
                                                </GridPane>
                                             </children></AnchorPane>
                                    </content>
                                  </Tab>
                                  <Tab text="Doctor">
                                    <content>
                                      <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                             <children>
                                                <GridPane layoutX="159.0" layoutY="142.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                                  <columnConstraints>
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                                                    <ColumnConstraints hgrow="SOMETIMES" maxWidth="200.0" minWidth="200.0" prefWidth="200.0" />
                                                    <ColumnConstraints hgrow="SOMETIMES" maxWidth="335.0" minWidth="10.0" prefWidth="335.0" />
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="50.0" prefWidth="200.0" />
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="10.0" prefWidth="200.0" />
                                                  </columnConstraints>
                                                  <rowConstraints>
                                                    <RowConstraints maxHeight="50.0" minHeight="50.0" prefHeight="50.0" vgrow="SOMETIMES" />
                                                    <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="90.0" minHeight="90.0" prefHeight="90.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="90.0" minHeight="90.0" prefHeight="90.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="90.0" minHeight="90.0" prefHeight="90.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="60.0" minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="60.0" minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
                                                    <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                  </rowConstraints>
                                                   <children>
                                                      <Label id="inputLabel1" maxHeight="30.0" maxWidth="160.0" minHeight="30.0" minWidth="160.0" stylesheets="@../styles/box.css" text="SLMC registration No" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                         <opaqueInsets>
                                                            <Insets />
                                                         </opaqueInsets>
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="80.0" maxWidth="160.0" minHeight="80.0" minWidth="160.0" stylesheets="@../styles/box.css" text="Education" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                         <opaqueInsets>
                                                            <Insets />
                                                         </opaqueInsets>
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="80.0" maxWidth="160.0" minHeight="80.0" minWidth="160.0" stylesheets="@../styles/box.css" text="Post Doctrol Training" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                                         <opaqueInsets>
                                                            <Insets />
                                                         </opaqueInsets>
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="80.0" maxWidth="160.0" minHeight="80.0" minWidth="160.0" stylesheets="@../styles/box.css" text="Experience" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                                         <opaqueInsets>
                                                            <Insets />
                                                         </opaqueInsets>
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="50.0" maxWidth="160.0" minHeight="50.0" minWidth="160.0" stylesheets="@../styles/box.css" text="Acedemic Achivements" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                                         <opaqueInsets>
                                                            <Insets />
                                                         </opaqueInsets>
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="50.0" maxWidth="160.0" minHeight="50.0" minWidth="160.0" stylesheets="@../styles/box.css" text="Other" GridPane.columnIndex="1" GridPane.rowIndex="6">
                                                         <opaqueInsets>
                                                            <Insets />
                                                         </opaqueInsets>
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <TextField id="inputText1" fx:id="doctorRegistarionNo" disable="true" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                                                      <TextArea id="inputText1" fx:id="doctorEducation" disable="true" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="2">
                                                         <GridPane.margin>
                                                            <Insets bottom="5.0" top="5.0" />
                                                         </GridPane.margin>
                                                      </TextArea>
                                                      <TextArea id="inputText1" fx:id="doctorTraining" disable="true" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="3">
                                                         <GridPane.margin>
                                                            <Insets bottom="5.0" top="5.0" />
                                                         </GridPane.margin>
                                                      </TextArea>
                                                      <TextArea id="inputText1" fx:id="doctorExperience" disable="true" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="4">
                                                         <GridPane.margin>
                                                            <Insets bottom="5.0" top="5.0" />
                                                         </GridPane.margin>
                                                      </TextArea>
                                                      <TextArea id="inputText1" fx:id="doctorAchivements" disable="true" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="5">
                                                         <GridPane.margin>
                                                            <Insets bottom="5.0" top="5.0" />
                                                         </GridPane.margin>
                                                      </TextArea>
                                                      <TextArea id="inputText1" fx:id="doctorOther" disable="true" prefHeight="200.0" prefWidth="200.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="6">
                                                         <GridPane.margin>
                                                            <Insets bottom="5.0" top="5.0" />
                                                         </GridPane.margin>
                                                      </TextArea>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                         <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="USER_MD" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="80.0" maxWidth="30.0" minHeight="80.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                          <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="LEANPUB" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="80.0" maxWidth="30.0" minHeight="80.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                          <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="HEARTBEAT" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="80.0" maxWidth="30.0" minHeight="80.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                          <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="STETHOSCOPE" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="50.0" maxWidth="30.0" minHeight="50.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="5">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                          <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="TROPHY" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="50.0" maxWidth="30.0" minHeight="50.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="6">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                          <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="STAR" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Button id="dark-blue" fx:id="editDoctorInfoButton" maxHeight="30.0" maxWidth="60.0" minHeight="30.0" minWidth="60.0" mnemonicParsing="false" onAction="#editDoctorInfo" stylesheets="@../styles/buttons.css" text="Edit" GridPane.columnIndex="1" GridPane.rowIndex="7">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                      </Button>
                                                   </children>
                                                </GridPane>
                                             </children></AnchorPane>
                                    </content>
                                  </Tab>
                                  <Tab text="Availability">
                                    <content>
                                      <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                             <children>
                                                <Label id="box" layoutX="103.0" layoutY="62.0" prefHeight="366.0" prefWidth="165.0" stylesheets="@../styles/box.css" AnchorPane.leftAnchor="50.0" AnchorPane.rightAnchor="100.0" AnchorPane.topAnchor="70.0" />
                                                <Pagination fx:id="availabilityPagination" layoutX="71.0" layoutY="26.0" maxPageIndicatorCount="5" pageCount="10" prefHeight="345.0" prefWidth="228.0" AnchorPane.leftAnchor="60.0" AnchorPane.rightAnchor="110.0" AnchorPane.topAnchor="80.0" />
                                                <TableView fx:id="availabilityTable" layoutX="53.0" layoutY="87.0" prefHeight="276.0" prefWidth="214.0" stylesheets="@../styles/table" AnchorPane.leftAnchor="60.0" AnchorPane.rightAnchor="110.0" AnchorPane.topAnchor="80.0">
                                                  <columnResizePolicy>
                                                        <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
                                                    </columnResizePolicy>
                                                      <columns>
                                                        <TableColumn maxWidth="30" minWidth="30" prefWidth="30" />  
                                                        <TableColumn maxWidth="200.0" minWidth="100.0" prefWidth="100.0" text="Date">
                                                            <cellValueFactory>
                                                                <PropertyValueFactory property="date" />
                                                            </cellValueFactory>
                                                        </TableColumn>
                                                        <TableColumn maxWidth="350.0" minWidth="120.0" prefWidth="120.0" text="Time">
                                                            <cellValueFactory>
                                                                <PropertyValueFactory property="time" />
                                                            </cellValueFactory>
                                                        </TableColumn>
                                                     </columns>
                                                     <items>
                                                        <FXCollections fx:factory="observableArrayList">
                                                            <Availability date="Monday" time="Time" />
                                                            <Availability date="Monday" time="Time" />
                                                            <Availability date="Monday" time="Time" />
                                                            <Availability date="Monday" time="Time" />
                                                            <Availability date="Monday" time="Time" />
                                                        </FXCollections>
                                                    </items>
                                                </TableView>
                                                <Button id="dark-blue" layoutX="280.0" layoutY="382.0" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" mnemonicParsing="false" onAction="#removeTimeSlot" stylesheets="@../styles/buttons.css" text="Add" AnchorPane.rightAnchor="110.0">
                                                    <graphic>  
                                                            
                                                        <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="MINUS" textFill="#eee" />
                                                            
                                                    </graphic>
                                                </Button>
                                                <Button id="dark-blue" layoutX="278.0" layoutY="382.0" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" mnemonicParsing="false" onAction="#addTimeSlot" stylesheets="@../styles/buttons.css" text="Add" AnchorPane.rightAnchor="150.0">
                                                   <graphic>
                                                      <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="PLUS" textFill="#eee" />
                                                   </graphic>
                                                </Button>        
                                             </children></AnchorPane>
                                    </content>
                                  </Tab>
                                  <Tab text="Account">
                                    <content>
                                      <AnchorPane minHeight="0.0" minWidth="0.0" prefHeight="180.0" prefWidth="200.0">
                                             <children>
                                                <GridPane layoutX="43.0" layoutY="72.0" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                                                  <columnConstraints>
                                                    <ColumnConstraints hgrow="SOMETIMES" maxWidth="50.0" minWidth="50.0" prefWidth="50.0" />
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="170.0" minWidth="170.0" prefWidth="170.0" />
                                                      <ColumnConstraints hgrow="SOMETIMES" maxWidth="300.0" minWidth="10.0" prefWidth="300.0" />
                                                    <ColumnConstraints hgrow="SOMETIMES" maxWidth="1.7976931348623157E308" minWidth="50.0" prefWidth="100.0" />
                                                  </columnConstraints>
                                                  <rowConstraints>
                                                    <RowConstraints maxHeight="60.0" minHeight="60.0" prefHeight="60.0" vgrow="SOMETIMES" />
                                                    <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                      <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                                    <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                                  </rowConstraints>
                                                   <children>
                                                      <Label id="inputLabel1" maxHeight="30.0" maxWidth="130.0" minHeight="30.0" minWidth="130.0" stylesheets="@../styles/box.css" text="User Name" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="30.0" maxWidth="130.0" minHeight="30.0" minWidth="130.0" stylesheets="@../styles/box.css" text="User Type" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="30.0" maxWidth="130.0" minHeight="30.0" minWidth="130.0" stylesheets="@../styles/box.css" text="User ID" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <TextField id="inputText1" fx:id="doctorUserName" disable="true" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                                                      <TextField id="inputText1" fx:id="doctorUserType" disable="true" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="2" />
                                                      <TextField id="inputText1" fx:id="doctorUserID" disable="true" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="3" />
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                         <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="USER" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                         <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="USERS" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                         <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="BULLSEYE" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="8">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                         <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="KEY" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="6">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                         <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="UNLOCK_ALT" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <Label id="inputLabel0" alignment="CENTER" contentDisplay="CENTER" maxHeight="30.0" maxWidth="30.0" minHeight="30.0" minWidth="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="1" GridPane.rowIndex="7">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                         <graphic>  
                                                            
                                                            <Glyph fontFamily="FontAwesome" fontSize="15.0" icon="UNLOCK" textFill="#333" />
                                                            
                                                        </graphic>
                                                      </Label>
                                                      <TextField id="inputText1" fx:id="doctorConfirmPassword" disable="true" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="8" />
                                                      <TextField id="inputText1" fx:id="doctorNewPassword" disable="true" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="7" />
                                                      <TextField id="inputText1" fx:id="doctorPassword" disable="true" maxHeight="30.0" minHeight="30.0" stylesheets="@../styles/box.css" GridPane.columnIndex="2" GridPane.rowIndex="6" />
                                                      <Label id="inputLabel1" maxHeight="30.0" maxWidth="130.0" minHeight="30.0" minWidth="130.0" stylesheets="@../styles/box.css" text="Confirm Password" GridPane.columnIndex="1" GridPane.rowIndex="8">
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="30.0" maxWidth="130.0" minHeight="30.0" minWidth="130.0" stylesheets="@../styles/box.css" text="new Password" GridPane.columnIndex="1" GridPane.rowIndex="7">
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Label id="inputLabel1" maxHeight="30.0" maxWidth="130.0" minHeight="30.0" minWidth="130.0" stylesheets="@../styles/box.css" text="Current Password" GridPane.columnIndex="1" GridPane.rowIndex="6">
                                                         <GridPane.margin>
                                                            <Insets left="40.0" />
                                                         </GridPane.margin>
                                                      </Label>
                                                      <Button id="dark-blue" fx:id="editUserInfoButton" maxHeight="30.0" maxWidth="60.0" minHeight="30.0" minWidth="60.0" mnemonicParsing="false" onAction="#editUserInfo" stylesheets="@../styles/buttons.css" text="Edit" GridPane.columnIndex="1" GridPane.rowIndex="4">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                      </Button>
                                                      <Button id="dark-blue" fx:id="editPasswordInfoButton" maxHeight="30.0" maxWidth="60.0" minHeight="30.0" minWidth="60.0" mnemonicParsing="false" onAction="#editPasswordInfo" stylesheets="@../styles/buttons.css" text="Edit" GridPane.columnIndex="1" GridPane.rowIndex="9">
                                                         <GridPane.margin>
                                                            <Insets left="10.0" />
                                                         </GridPane.margin>
                                                      </Button>
                                                   </children>
                                                </GridPane>
                                             </children></AnchorPane>
                                    </content>
                                  </Tab>
                                </tabs>
                              </TabPane>
                              <GridPane GridPane.columnIndex="1" GridPane.rowIndex="2">
                                <columnConstraints>
                                  <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                                </columnConstraints>
                                <rowConstraints>
                                  <RowConstraints maxHeight="39.0" minHeight="39.0" prefHeight="39.0" vgrow="SOMETIMES" />
                                  <RowConstraints maxHeight="275.0" minHeight="275.0" prefHeight="275.0" vgrow="SOMETIMES" />
                                    <RowConstraints maxHeight="40.0" minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                  <RowConstraints maxHeight="572.0" minHeight="10.0" prefHeight="167.0" vgrow="SOMETIMES" />
                                    <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                </rowConstraints>
                                 <children>
                                    <Label id="profileBox" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/box.css" GridPane.rowIndex="1" GridPane.rowSpan="4" />
                                    <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/header.css" />
                                    <ImageView fx:id="profileImage" fitHeight="180.0" fitWidth="180.0" pickOnBounds="true" preserveRatio="true" GridPane.rowIndex="1">
                                       <GridPane.margin>
                                          <Insets left="10.0" top="60.0" />
                                       </GridPane.margin>
                                       <image>
                                          <Image url="@../imgs/profile.png" />
                                       </image>
                                    </ImageView>
                                    <Button id="dark-blue" fx:id="editProfilePicButton" maxHeight="30.0" maxWidth="1.7976931348623157E308" minHeight="30.0" minWidth="65.0" mnemonicParsing="false" onAction="#editProfilePic" stylesheets="@../styles/buttons.css" text="Edit" GridPane.rowIndex="2">
                                       <GridPane.margin>
                                          <Insets left="10.0" right="10.0" />
                                       </GridPane.margin>
                                    </Button>
                                 </children>
                              </GridPane>
                           </children>
                        </GridPane>
                     </children></AnchorPane>
            </content>
          </Tab>
        </tabs>
      </TabPane>
      <GridPane layoutX="22.0" layoutY="5.0" maxHeight="51.0" maxWidth="1.7976931348623157E308" prefHeight="51.0" prefWidth="778.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
        <columnConstraints>
          <ColumnConstraints hgrow="SOMETIMES" maxWidth="170.0" minWidth="170.0" prefWidth="170.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="257.0" minWidth="10.0" prefWidth="139.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="1000.0" minWidth="10.0" prefWidth="287.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="60.0" minWidth="60.0" prefWidth="60.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="60.0" minWidth="60.0" prefWidth="60.0" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="60.0" minWidth="60.0" prefWidth="60.0" />
          <ColumnConstraints hgrow="SOMETIMES" maxWidth="20.0" minWidth="8.0" prefWidth="20.0" />
        </columnConstraints>
        <rowConstraints>
          <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
        </rowConstraints>
         <children>
            <Label alignment="BASELINE_CENTER" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="40.0" prefWidth="78.0" stylesheets="@../styles/header.css" GridPane.columnIndex="3">    
            
            </Label>
            <Label alignment="BASELINE_CENTER" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" minWidth="-Infinity" stylesheets="@../styles/header.css" GridPane.columnIndex="4">
             
            </Label>    
            
            <Label alignment="BASELINE_CENTER" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" minWidth="-Infinity" stylesheets="@../styles/header.css" textFill="#e1dede" GridPane.columnIndex="5">
             
            </Label>
            <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/header.css" GridPane.columnIndex="1" />
            <Label alignment="CENTER" maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="51.0" prefWidth="233.0" stylesheets="@../styles/header.css" text=" HealthPlus" textFill="#d7d6d6">
               <font>
                  <Font name="System Bold" size="18.0" />
               </font>
               <graphic>
               
                   <FontAwesomeIconView fill="#ff6666" glyphName="PLUS_CIRCLE" glyphStyle="" size="20px" />
                 
               </graphic>
               <effect>
                  <Bloom threshold="0.84" />
               </effect> 
            </Label>
            <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" prefHeight="55.0" prefWidth="248.0" stylesheets="@../styles/header.css" GridPane.columnIndex="2" />
            <Label maxHeight="1.7976931348623157E308" maxWidth="1.7976931348623157E308" stylesheets="@../styles/header.css" GridPane.columnIndex="6" />
            <Button id="logout" fx:id="logoutButton" maxWidth="40.0" minWidth="40.0" mnemonicParsing="false" onAction="#logout" stylesheets="@../styles/box.css" GridPane.columnIndex="5">
               <GridPane.margin>
                  <Insets left="10.0" />
               </GridPane.margin>
               <graphic>
                    
                   <FontAwesomeIconView fill="#ffffff" glyphName="SIGN_OUT" glyphStyle="" size="15px" />
                      
               </graphic>
               <cursor>
                  <Cursor fx:constant="HAND" />
               </cursor>
            </Button>
            <Button id="iconButton" fx:id="AllMessages" maxWidth="40.0" minWidth="40.0" mnemonicParsing="false" onAction="#showAllMessages" stylesheets="@../styles/box.css" GridPane.columnIndex="4">
               <graphic>
                    
                  <FontAwesomeIconView fill="#222" glyphName="ENVELOPE" glyphStyle="" size="15px" />
                   
                </graphic>
                <GridPane.margin>
                  <Insets left="10.0" />
               </GridPane.margin>
               <cursor>
                  <Cursor fx:constant="HAND" />
               </cursor>
            </Button>
            <Button id="iconButton" fx:id="showUserButton" maxWidth="40.0" minWidth="40.0" mnemonicParsing="false" onAction="#showUser" stylesheets="@../styles/box.css" GridPane.columnIndex="3">
               <GridPane.margin>
                  <Insets left="10.0" />
               </GridPane.margin>
                <graphic>
                    
                    <FontAwesomeIconView fill="#222" glyphName="USER" glyphStyle="" size="15px" />
                      
                </graphic>
               <cursor>
                  <Cursor fx:constant="HAND" />
               </cursor>
               
            </Button>
            </children>
      </GridPane>
    </children>
</fx:root>
